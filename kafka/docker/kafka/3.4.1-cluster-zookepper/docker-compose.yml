# https://hub.docker.com/r/bitnami/kafka
# https://github.com/bitnami/containers/blob/main/bitnami/kafka/docker-compose-cluster.yml

version: '3'

# 定义通用配置
x-kafka-common: &kafka-common
  image: registry.cn-hangzhou.aliyuncs.com/zhengqing/kafka:3.4.1   # 原镜像`bitnami/kafka:3.4.1`
  restart: unless-stopped                                          # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
  depends_on:
    - zookepper
  links:
    - zookepper
x-kafka-common-env: &kafka-common-env
  KAFKA_ENABLE_KRAFT: no # 是否启用Kafka Raft（KRaft）模式。默认值：是
  ALLOW_PLAINTEXT_LISTENER: yes # 允许使用 PLAINTEXT 监听器。默认值：否。
  KAFKA_CFG_ZOOKEEPER_CONNECT: zookepper:2181    # zookeeper连接地址
  KAFKA_CFG_INTER_BROKER_LISTENER_NAME: INTERNAL   # 用于配置broker之间通信使用的监听器名称
  KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT # 监听的协议集合多个。（这里INTERNAL指的是内部，EXTERNAL是外部，之后就不能够使用PLAINTEXT作为协议名了，只能够使用定义的INTERNAL、EXTERNAL）

# 网桥 -> 方便相互通讯
networks:
  kafka:
    ipam:
      driver: default
      config:
        - subnet: "**********/24"

services:
  zookepper:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/zookeeper:latest   # 原镜像`bitnami/zookeeper:latest`
    container_name: zookeeper-server                                      # 容器名为'zookeeper-server'
    restart: unless-stopped                                               # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
    volumes:                                         # 数据卷挂载路径设置,将本机目录映射到容器目录
      - "/etc/localtime:/etc/localtime"
      - "./kafka/zookeeper:/bitnami/zookeeper"
    environment:
      ALLOW_ANONYMOUS_LOGIN: yes
    ports:                                           # 映射端口
      - "2181:2181"
    networks:
      kafka:
        ipv4_address: ***********

  kafka-1:
    container_name: kafka-1
    <<: *kafka-common
    volumes:
      - "/etc/localtime:/etc/localtime"
      - "./kafka/kafka-1:/bitnami/kafka"
    environment:
      <<: *kafka-common-env
      KAFKA_CFG_BROKER_ID: 1
      KAFKA_CFG_LISTENERS: INTERNAL://:9092,EXTERNAL://0.0.0.0:9093  # kafka监听地址
      KAFKA_CFG_ADVERTISED_LISTENERS: INTERNAL://kafka-1:9092,EXTERNAL://host.docker.internal:9093   # TODO 外网访问填写域名或主机IP -- 让客户端能够监听消息  （ host.docker.internal：自动识别主机IP，在Windows或Mac上运行Docker有效 ）
    ports:
      - "9093:9093"
    networks:
      kafka:
        ipv4_address: ***********
  kafka-2:
    container_name: kafka-2
    <<: *kafka-common
    volumes:
      - "/etc/localtime:/etc/localtime"
      - "./kafka/kafka-2:/bitnami/kafka"
    environment:
      <<: *kafka-common-env
      KAFKA_CFG_BROKER_ID: 2
      KAFKA_CFG_LISTENERS: INTERNAL://:9092,EXTERNAL://0.0.0.0:9094  # kafka监听地址
      KAFKA_CFG_ADVERTISED_LISTENERS: INTERNAL://kafka-2:9092,EXTERNAL://host.docker.internal:9094   # TODO 外网访问填写域名或主机IP -- 让客户端能够监听消息  （ host.docker.internal：自动识别主机IP，在Windows或Mac上运行Docker有效 ）
    ports:
      - "9094:9094"
    networks:
      kafka:
        ipv4_address: ***********

  # kafka-map图形化管理工具
  kafka-map:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/kafka-map     # 原镜像`dushixiang/kafka-map:latest`
    container_name: kafka-map                                        # 容器名为'kafka-map'
    restart: unless-stopped                                          # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
    volumes:
      - "./kafka/kafka-map/data:/usr/local/kafka-map/data"
    environment:
      DEFAULT_USERNAME: admin
      DEFAULT_PASSWORD: 123456
    ports:                              # 映射端口
      - "9006:8080"
    depends_on:                         # 解决容器依赖启动先后问题
      - kafka-1
      - kafka-2
    links:                              # 配置容器互相连接
      - kafka-1
      - kafka-2
    networks:
      kafka:
        ipv4_address: ***********
