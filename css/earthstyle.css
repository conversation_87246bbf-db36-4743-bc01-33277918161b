html,
body {
    height: 100%;
}

body {
    background-color: #000000;
    margin: 0;
    font-family: Helvetica, sans-serif;
    ;
    overflow: hidden;
}

a {
    color: #ffffff;
}

#info {
    position: absolute;
    width: 100%;
    color: #ffffff;
    padding: 5px;
    font-family: Monospace;
    font-size: 13px;
    font-weight: bold;
    text-align: center;
    z-index: 1;
}

#menu {
    /* opacity: 0; */
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
}

.element {
    width: 100px;
    /* 120 160 */
    height: 100px;
    box-shadow: 0px 0px 12px rgba(0, 255, 255, 0.5);
    border: 1px solid rgba(127, 255, 255, 0.25);
    text-align: center;
    cursor: default;
}

.element:hover {
    box-shadow: 0px 0px 12px rgba(0, 255, 255, 0.75);
    border: 1px solid rgba(127, 255, 255, 0.75);
}

.element img {
    width: 120px;
    height: 150px;
}

.element .number {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 12px;
    color: rgba(127, 255, 255, 0.75);
}

.element .symbol {
    position: absolute;
    top: 40px;
    left: 0px;
    right: 0px;
    font-size: 60px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.75);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.95);
}

.element .details {
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    font-size: 12px;
    color: rgba(127, 255, 255, 0.75);
}

button {
    color: rgba(127, 255, 255, 0.75);
    background: transparent;
    outline: 1px solid rgba(127, 255, 255, 0.75);
    border: 0px;
    padding: 5px 10px;
    cursor: pointer;
}

button:hover {
    background-color: rgba(0, 255, 255, 0.5);
}

button:active {
    color: #000000;
    background-color: rgba(0, 255, 255, 0.75);
}

.show_info {
    position: fixed;
    background-color: rgba(0, 0, 0, .6);
    padding: 10px;
    width: 300px;
    margin: 0 auto;
    left: 0;
    right: 0;
    border-radius: 5px;
    box-shadow: 0 0 10px 0 #fff;
    top: 30%;
}

.show_info img {
    display: block;
    margin: auto;
    border-radius: 5px;
    box-shadow: 0 0 10px 0 #888;
}

.show_info .intro {
    color: #fff;
    text-indent: 20px;
    margin-top: 10px;
    height: 65px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.show_info .info_my {
    text-align: center;
}

.show_info .info_my>* {
    display: inline-block !important;
    vertical-align: middle;
}

.show_info .info_my .info_mem {
    color: #fff;
    max-width: 250px;
}

.show_info .info_my .info_mem>div {
    text-align: left;
}

.show_info .info_my .info_mem>div.nickname {
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}