package com.example.user.dto;

import com.example.user.constant.UserConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户数据传输对象")
public class UserDTO implements Serializable {

    @Schema(description = "用户ID", example = "1", accessMode = Schema.AccessMode.READ_ONLY)
    private Long id;

    @Schema(
        description = "用户名，必须唯一，只能包含字母、数字和下划线",
        example = "testuser",
        minLength = 4,
        maxLength = 20,
        pattern = "^[a-zA-Z0-9_]+$"
    )
    @NotBlank(message = "{user.username.notBlank}")
    @Size(min = 4, max = 20, message = "{user.username.size}")
    private String username;

    @Schema(
        description = "用户密码，至少6位，建议包含字母和数字",
        example = "123456",
        minLength = 6,
        maxLength = 20,
        format = "password"
    )
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String password;

    @Schema(
        description = "用户昵称，用于显示",
        example = "测试用户",
        maxLength = 50
    )
    @Size(max = 50, message = "昵称长度不能超过50")
    private String nickname;

    @Schema(
        description = "用户邮箱地址，必须是有效的邮箱格式",
        example = "<EMAIL>",
        format = "email"
    )
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(
        description = "用户头像URL",
        example = "https://example.com/avatar.jpg",
        format = "uri"
    )
    private String avatar;

    @Schema(description = "创建时间", example = "2023-12-11T10:30:00", accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime createdAt;

    @Schema(description = "最后更新时间", example = "2023-12-11T15:45:00", accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime updatedAt;
}