package com.example.user.api;

import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户管理", description = "用户相关操作接口，包括注册、登录、认证、用户信息管理等功能")
@Validated
public interface UserApi {

    @Operation(
        summary = "用户注册",
        description = "创建新用户账户。用户名必须唯一，密码会自动加密存储。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "注册成功",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Result.class),
                examples = @ExampleObject(
                    name = "成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "avatar": null,
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T10:30:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "参数错误",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "参数错误示例",
                    value = """
                    {
                      "success": false,
                      "code": "400",
                      "message": "用户名已存在",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @PostMapping("/api/users/register")
    Result<UserDTO> register(
        @Parameter(
            description = "用户注册信息",
            required = true,
            schema = @Schema(implementation = UserDTO.class)
        )
        @Validated @RequestBody UserDTO userDTO
    );

    @Operation(
        summary = "用户登录",
        description = "用户登录验证，成功后返回 JWT Token。Token 有效期为 24 小时。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "登录成功",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "登录成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYzOTIwMDAwMCwiZXhwIjoxNjM5Mjg2NDAwfQ.signature"
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "认证失败",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "认证失败示例",
                    value = """
                    {
                      "success": false,
                      "code": "401",
                      "message": "用户名或密码错误",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @PostMapping("/api/users/login")
    Result<String> login(
        @Parameter(description = "用户名", required = true, example = "testuser")
        @RequestParam("username") String username,

        @Parameter(description = "密码", required = true, example = "123456")
        @RequestParam("password") String password
    );

    @Operation(
        summary = "Token 验证",
        description = "验证 JWT Token 的有效性，并返回当前用户信息。需要在请求头中携带 Authorization。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Token 有效",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "验证成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "avatar": null,
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T10:30:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Token 无效或已过期",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "Token 无效示例",
                    value = """
                    {
                      "success": false,
                      "code": "401",
                      "message": "Token 无效或已过期",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @GetMapping("/api/users/validate")
    Result<UserDTO> validateToken(
        @Parameter(
            description = "JWT Token，格式：Bearer {token}",
            required = true,
            example = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYzOTIwMDAwMCwiZXhwIjoxNjM5Mjg2NDAwfQ.signature"
        )
        @RequestHeader("Authorization") String token
    );