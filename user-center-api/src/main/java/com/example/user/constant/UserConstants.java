package com.example.user.constant;

public final class UserConstants {
    private UserConstants() {}

    // API路径常量
    public static final String API_PREFIX = "/api/users";
    public static final String REGISTER_PATH = "/register";
    public static final String LOGIN_PATH = "/login";
    public static final String VALIDATE_PATH = "/validate";

    // 角色常量
    public static final String ROLE_USER = "USER";
    public static final String ROLE_ADMIN = "ADMIN";

    // 错误码常量
    public static final String ERROR_USER_NOT_FOUND = "USER_NOT_FOUND";
    public static final String ERROR_USER_EXISTS = "USER_EXISTS";
    public static final String ERROR_USERNAME_EXISTS = "USERNAME_EXISTS";
    public static final String ERROR_INVALID_PASSWORD = "INVALID_PASSWORD";
    public static final String ERROR_INVALID_TOKEN = "INVALID_TOKEN";
    public static final String ERROR_SYSTEM = "SYSTEM_ERROR";
    public static final String ERROR_LOGIN_FAILED = "LOGIN_FAILED";
    public static final String ERROR_REGISTRATION_FAILED = "REGISTRATION_FAILED";

    // 消息常量
    public static final String MSG_USER_NOT_FOUND = "用户不存在";
    public static final String MSG_USER_EXISTS = "用户名已存在";
    public static final String MSG_USERNAME_EXISTS = "用户名已存在";
    public static final String MSG_INVALID_PASSWORD = "密码错误";
    public static final String MSG_PASSWORD_INCORRECT = "密码错误";
    public static final String MSG_INVALID_TOKEN = "无效的token";
    public static final String MSG_TOKEN_INVALID = "无效的token";
    public static final String MSG_TOKEN_EXPIRED = "token已过期";
    public static final String MSG_SYSTEM_ERROR = "系统错误,请稍后重试";
}