package com.example.multithreading.synchronization;

import lombok.extern.slf4j.Slf4j;

/**
 * synchronized 同步机制演示
 * 
 * 知识点：
 * 1. synchronized 关键字的使用
 * 2. 同步方法 vs 同步代码块
 * 3. 对象锁 vs 类锁
 * 4. 线程安全问题的解决
 * 5. 死锁问题
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class SynchronizedDemo {

    private int count = 0;
    private static int staticCount = 0;
    
    // 两个不同的锁对象，用于演示死锁
    private static final Object lock1 = new Object();
    private static final Object lock2 = new Object();

    public static void main(String[] args) throws InterruptedException {
        SynchronizedDemo demo = new SynchronizedDemo();
        
        log.info("=== synchronized 同步机制演示 ===");
        
        // 1. 演示线程安全问题
        demo.demonstrateThreadSafetyProblem();
        
        // 2. 使用synchronized解决线程安全问题
        demo.demonstrateSynchronizedSolution();
        
        // 3. 同步方法演示
        demo.demonstrateSynchronizedMethods();
        
        // 4. 同步代码块演示
        demo.demonstrateSynchronizedBlocks();
        
        // 5. 类锁演示
        demo.demonstrateClassLock();
        
        // 6. 死锁演示
        demo.demonstrateDeadlock();
    }

    /**
     * 演示线程安全问题
     */
    private void demonstrateThreadSafetyProblem() throws InterruptedException {
        log.info("--- 1. 线程安全问题演示 ---");
        
        count = 0; // 重置计数器
        
        // 创建多个线程同时修改共享变量
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    count++; // 非线程安全的操作
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        log.info("期望结果: 10000, 实际结果: {} (可能不一致)", count);
        log.info("线程安全问题演示完成\n");
    }

    /**
     * 使用synchronized解决线程安全问题
     */
    private void demonstrateSynchronizedSolution() throws InterruptedException {
        log.info("--- 2. synchronized解决方案演示 ---");
        
        count = 0; // 重置计数器
        
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    synchronizedIncrement(); // 线程安全的操作
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
        
        log.info("期望结果: 10000, 实际结果: {} (应该一致)", count);
        log.info("synchronized解决方案演示完成\n");
    }

    /**
     * 同步方法
     */
    private synchronized void synchronizedIncrement() {
        count++;
    }

    /**
     * 同步方法演示
     */
    private void demonstrateSynchronizedMethods() throws InterruptedException {
        log.info("--- 3. 同步方法演示 ---");
        
        BankAccount account = new BankAccount(1000);
        
        // 创建多个线程同时进行存取款操作
        Thread depositor = new Thread(() -> {
            for (int i = 0; i < 5; i++) {
                account.deposit(100);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        Thread withdrawer = new Thread(() -> {
            for (int i = 0; i < 5; i++) {
                account.withdraw(80);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        depositor.start();
        withdrawer.start();
        
        depositor.join();
        withdrawer.join();
        
        log.info("最终余额: {}", account.getBalance());
        log.info("同步方法演示完成\n");
    }

    /**
     * 同步代码块演示
     */
    private void demonstrateSynchronizedBlocks() throws InterruptedException {
        log.info("--- 4. 同步代码块演示 ---");
        
        StringBuilder sb = new StringBuilder();
        Object lock = new Object();
        
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    synchronized (lock) {
                        sb.append("Thread-").append(threadId).append("-").append(j).append(" ");
                        log.info("线程 {} 添加内容: Thread-{}-{}", 
                            Thread.currentThread().getName(), threadId, j);
                    }
                    
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
        
        log.info("最终字符串: {}", sb.toString());
        log.info("同步代码块演示完成\n");
    }

    /**
     * 类锁演示
     */
    private void demonstrateClassLock() throws InterruptedException {
        log.info("--- 5. 类锁演示 ---");
        
        staticCount = 0;
        
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    incrementStaticCount();
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
        
        log.info("静态变量最终值: {}", staticCount);
        log.info("类锁演示完成\n");
    }

    /**
     * 静态同步方法（类锁）
     */
    private static synchronized void incrementStaticCount() {
        staticCount++;
    }

    /**
     * 死锁演示
     */
    private void demonstrateDeadlock() {
        log.info("--- 6. 死锁演示 ---");
        log.info("注意：这个演示可能导致死锁，程序会卡住");
        
        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                log.info("线程1获得lock1");
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                log.info("线程1尝试获得lock2");
                synchronized (lock2) {
                    log.info("线程1获得lock2");
                }
            }
        });
        
        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                log.info("线程2获得lock2");
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                log.info("线程2尝试获得lock1");
                synchronized (lock1) {
                    log.info("线程2获得lock1");
                }
            }
        });
        
        thread1.start();
        thread2.start();
        
        // 等待一段时间后强制结束，避免真正的死锁
        try {
            Thread.sleep(2000);
            if (thread1.isAlive() || thread2.isAlive()) {
                log.warn("检测到死锁！强制中断线程");
                thread1.interrupt();
                thread2.interrupt();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("死锁演示完成");
    }

    /**
     * 银行账户类 - 演示同步方法
     */
    static class BankAccount {
        private double balance;
        
        public BankAccount(double initialBalance) {
            this.balance = initialBalance;
        }
        
        public synchronized void deposit(double amount) {
            balance += amount;
            log.info("存款 {}, 当前余额: {}", amount, balance);
        }
        
        public synchronized void withdraw(double amount) {
            if (balance >= amount) {
                balance -= amount;
                log.info("取款 {}, 当前余额: {}", amount, balance);
            } else {
                log.warn("余额不足，无法取款 {}, 当前余额: {}", amount, balance);
            }
        }
        
        public synchronized double getBalance() {
            return balance;
        }
    }
}
