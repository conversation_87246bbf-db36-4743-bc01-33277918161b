package com.example.multithreading.basic;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;

/**
 * 线程创建方式演示
 * 
 * 知识点：
 * 1. 继承Thread类
 * 2. 实现Runnable接口
 * 3. 实现Callable接口
 * 4. 使用Lambda表达式
 * 5. 线程的基本操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ThreadCreationDemo {

    public static void main(String[] args) throws InterruptedException, ExecutionException {
        log.info("=== 线程创建方式演示 ===");
        
        // 方式1：继承Thread类
        demonstrateThreadClass();
        
        // 方式2：实现Runnable接口
        demonstrateRunnableInterface();
        
        // 方式3：实现Callable接口
        demonstrateCallableInterface();
        
        // 方式4：使用Lambda表达式
        demonstrateLambdaExpression();
        
        // 线程基本操作
        demonstrateThreadOperations();
    }

    /**
     * 方式1：继承Thread类
     */
    private static void demonstrateThreadClass() throws InterruptedException {
        log.info("--- 方式1：继承Thread类 ---");
        
        class MyThread extends Thread {
            private final String name;
            
            public MyThread(String name) {
                this.name = name;
            }
            
            @Override
            public void run() {
                for (int i = 1; i <= 5; i++) {
                    log.info("线程 {} 执行第 {} 次", name, i);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("线程 {} 被中断", name);
                        break;
                    }
                }
            }
        }
        
        MyThread thread1 = new MyThread("Thread-1");
        MyThread thread2 = new MyThread("Thread-2");
        
        thread1.start();
        thread2.start();
        
        // 等待线程完成
        thread1.join();
        thread2.join();
        
        log.info("继承Thread类方式演示完成\n");
    }

    /**
     * 方式2：实现Runnable接口
     */
    private static void demonstrateRunnableInterface() throws InterruptedException {
        log.info("--- 方式2：实现Runnable接口 ---");
        
        class MyRunnable implements Runnable {
            private final String name;
            
            public MyRunnable(String name) {
                this.name = name;
            }
            
            @Override
            public void run() {
                for (int i = 1; i <= 3; i++) {
                    log.info("Runnable {} 执行第 {} 次", name, i);
                    try {
                        Thread.sleep(800);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("Runnable {} 被中断", name);
                        break;
                    }
                }
            }
        }
        
        Thread thread1 = new Thread(new MyRunnable("Task-1"));
        Thread thread2 = new Thread(new MyRunnable("Task-2"));
        
        thread1.start();
        thread2.start();
        
        thread1.join();
        thread2.join();
        
        log.info("实现Runnable接口方式演示完成\n");
    }

    /**
     * 方式3：实现Callable接口
     */
    private static void demonstrateCallableInterface() throws InterruptedException, ExecutionException {
        log.info("--- 方式3：实现Callable接口 ---");
        
        class MyCallable implements Callable<String> {
            private final String name;
            
            public MyCallable(String name) {
                this.name = name;
            }
            
            @Override
            public String call() throws Exception {
                int sum = 0;
                for (int i = 1; i <= 5; i++) {
                    sum += i;
                    log.info("Callable {} 计算中... 当前和: {}", name, sum);
                    Thread.sleep(500);
                }
                return String.format("Callable %s 计算结果: %d", name, sum);
            }
        }
        
        FutureTask<String> task1 = new FutureTask<>(new MyCallable("Calculator-1"));
        FutureTask<String> task2 = new FutureTask<>(new MyCallable("Calculator-2"));
        
        Thread thread1 = new Thread(task1);
        Thread thread2 = new Thread(task2);
        
        thread1.start();
        thread2.start();
        
        // 获取计算结果
        String result1 = task1.get(); // 阻塞等待结果
        String result2 = task2.get();
        
        log.info("结果1: {}", result1);
        log.info("结果2: {}", result2);
        
        log.info("实现Callable接口方式演示完成\n");
    }

    /**
     * 方式4：使用Lambda表达式
     */
    private static void demonstrateLambdaExpression() throws InterruptedException {
        log.info("--- 方式4：使用Lambda表达式 ---");
        
        // 使用Lambda创建线程
        Thread thread1 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                log.info("Lambda线程1 执行第 {} 次", i);
                try {
                    Thread.sleep(600);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        
        Thread thread2 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                log.info("Lambda线程2 执行第 {} 次", i);
                try {
                    Thread.sleep(600);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        
        thread1.start();
        thread2.start();
        
        thread1.join();
        thread2.join();
        
        log.info("Lambda表达式方式演示完成\n");
    }

    /**
     * 线程基本操作演示
     */
    private static void demonstrateThreadOperations() throws InterruptedException {
        log.info("--- 线程基本操作演示 ---");
        
        Thread thread = new Thread(() -> {
            try {
                for (int i = 1; i <= 10; i++) {
                    if (Thread.currentThread().isInterrupted()) {
                        log.info("线程收到中断信号，准备退出");
                        break;
                    }
                    log.info("工作线程执行第 {} 次", i);
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                log.info("线程在睡眠中被中断");
                Thread.currentThread().interrupt();
            }
        });
        
        // 设置线程名称
        thread.setName("WorkerThread");
        
        // 设置为守护线程
        thread.setDaemon(false);
        
        // 设置优先级
        thread.setPriority(Thread.NORM_PRIORITY);
        
        log.info("线程状态: {}", thread.getState());
        
        thread.start();
        log.info("线程状态: {}", thread.getState());
        
        // 让线程运行3秒后中断
        Thread.sleep(3000);
        log.info("发送中断信号");
        thread.interrupt();
        
        // 等待线程结束
        thread.join();
        log.info("线程状态: {}", thread.getState());
        
        log.info("线程基本操作演示完成");
    }
}
