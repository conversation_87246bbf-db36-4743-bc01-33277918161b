package com.example.multithreading.patterns;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 生产者消费者模式演示
 * 
 * 知识点：
 * 1. 使用BlockingQueue实现
 * 2. 使用wait/notify实现
 * 3. 使用Lock/Condition实现
 * 4. 多生产者多消费者场景
 * 5. 性能对比
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ProducerConsumerDemo {

    public static void main(String[] args) throws InterruptedException {
        log.info("=== 生产者消费者模式演示 ===");
        
        // 1. BlockingQueue实现
        demonstrateBlockingQueue();
        
        // 2. wait/notify实现
        demonstrateWaitNotify();
        
        // 3. Lock/Condition实现
        demonstrateLockCondition();
        
        // 4. 多生产者多消费者
        demonstrateMultipleProducersConsumers();
    }

    /**
     * 使用BlockingQueue实现生产者消费者
     */
    private static void demonstrateBlockingQueue() throws InterruptedException {
        log.info("--- 1. BlockingQueue实现 ---");
        
        BlockingQueue<String> queue = new ArrayBlockingQueue<>(5);
        AtomicInteger producedCount = new AtomicInteger(0);
        AtomicInteger consumedCount = new AtomicInteger(0);
        
        // 生产者
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 10; i++) {
                    String item = "Item-" + i;
                    queue.put(item); // 阻塞式添加
                    producedCount.incrementAndGet();
                    log.info("BlockingQueue生产者生产: {}, 队列大小: {}", item, queue.size());
                    Thread.sleep(100);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 消费者
        Thread consumer = new Thread(() -> {
            try {
                while (consumedCount.get() < 10) {
                    String item = queue.take(); // 阻塞式获取
                    consumedCount.incrementAndGet();
                    log.info("BlockingQueue消费者消费: {}, 队列大小: {}", item, queue.size());
                    Thread.sleep(150);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        producer.start();
        consumer.start();
        
        producer.join();
        consumer.join();
        
        log.info("BlockingQueue实现完成，生产: {}, 消费: {}\n", 
            producedCount.get(), consumedCount.get());
    }

    /**
     * 使用wait/notify实现生产者消费者
     */
    private static void demonstrateWaitNotify() throws InterruptedException {
        log.info("--- 2. wait/notify实现 ---");
        
        WaitNotifyBuffer buffer = new WaitNotifyBuffer(3);
        
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 8; i++) {
                    String item = "Product-" + i;
                    buffer.produce(item);
                    Thread.sleep(200);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 1; i <= 8; i++) {
                    buffer.consume();
                    Thread.sleep(300);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        producer.start();
        consumer.start();
        
        producer.join();
        consumer.join();
        
        log.info("wait/notify实现完成\n");
    }

    /**
     * wait/notify缓冲区实现
     */
    static class WaitNotifyBuffer {
        private final String[] buffer;
        private int count = 0;
        private int putIndex = 0;
        private int takeIndex = 0;
        
        public WaitNotifyBuffer(int capacity) {
            this.buffer = new String[capacity];
        }
        
        public synchronized void produce(String item) throws InterruptedException {
            while (count == buffer.length) {
                log.info("缓冲区已满，生产者等待");
                wait();
            }
            
            buffer[putIndex] = item;
            putIndex = (putIndex + 1) % buffer.length;
            count++;
            
            log.info("wait/notify生产者生产: {}, 缓冲区大小: {}", item, count);
            notifyAll(); // 通知消费者
        }
        
        public synchronized String consume() throws InterruptedException {
            while (count == 0) {
                log.info("缓冲区为空，消费者等待");
                wait();
            }
            
            String item = buffer[takeIndex];
            takeIndex = (takeIndex + 1) % buffer.length;
            count--;
            
            log.info("wait/notify消费者消费: {}, 缓冲区大小: {}", item, count);
            notifyAll(); // 通知生产者
            
            return item;
        }
    }

    /**
     * 使用Lock/Condition实现生产者消费者
     */
    private static void demonstrateLockCondition() throws InterruptedException {
        log.info("--- 3. Lock/Condition实现 ---");
        
        LockConditionBuffer buffer = new LockConditionBuffer(4);
        
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 6; i++) {
                    String item = "Goods-" + i;
                    buffer.produce(item);
                    Thread.sleep(250);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 1; i <= 6; i++) {
                    buffer.consume();
                    Thread.sleep(400);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        producer.start();
        consumer.start();
        
        producer.join();
        consumer.join();
        
        log.info("Lock/Condition实现完成\n");
    }

    /**
     * Lock/Condition缓冲区实现
     */
    static class LockConditionBuffer {
        private final ReentrantLock lock = new ReentrantLock();
        private final Condition notFull = lock.newCondition();
        private final Condition notEmpty = lock.newCondition();
        
        private final String[] buffer;
        private int count = 0;
        private int putIndex = 0;
        private int takeIndex = 0;
        
        public LockConditionBuffer(int capacity) {
            this.buffer = new String[capacity];
        }
        
        public void produce(String item) throws InterruptedException {
            lock.lock();
            try {
                while (count == buffer.length) {
                    log.info("缓冲区已满，生产者等待");
                    notFull.await();
                }
                
                buffer[putIndex] = item;
                putIndex = (putIndex + 1) % buffer.length;
                count++;
                
                log.info("Lock/Condition生产者生产: {}, 缓冲区大小: {}", item, count);
                notEmpty.signal(); // 通知消费者
                
            } finally {
                lock.unlock();
            }
        }
        
        public String consume() throws InterruptedException {
            lock.lock();
            try {
                while (count == 0) {
                    log.info("缓冲区为空，消费者等待");
                    notEmpty.await();
                }
                
                String item = buffer[takeIndex];
                takeIndex = (takeIndex + 1) % buffer.length;
                count--;
                
                log.info("Lock/Condition消费者消费: {}, 缓冲区大小: {}", item, count);
                notFull.signal(); // 通知生产者
                
                return item;
                
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 多生产者多消费者演示
     */
    private static void demonstrateMultipleProducersConsumers() throws InterruptedException {
        log.info("--- 4. 多生产者多消费者演示 ---");
        
        BlockingQueue<String> queue = new LinkedBlockingQueue<>(10);
        AtomicInteger totalProduced = new AtomicInteger(0);
        AtomicInteger totalConsumed = new AtomicInteger(0);
        
        int producerCount = 3;
        int consumerCount = 2;
        int itemsPerProducer = 5;
        
        CountDownLatch latch = new CountDownLatch(producerCount + consumerCount);
        
        // 创建多个生产者
        for (int i = 1; i <= producerCount; i++) {
            final int producerId = i;
            new Thread(() -> {
                try {
                    for (int j = 1; j <= itemsPerProducer; j++) {
                        String item = String.format("P%d-Item%d", producerId, j);
                        queue.put(item);
                        totalProduced.incrementAndGet();
                        log.info("生产者{} 生产: {}, 队列大小: {}", 
                            producerId, item, queue.size());
                        Thread.sleep(100);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 创建多个消费者
        for (int i = 1; i <= consumerCount; i++) {
            final int consumerId = i;
            new Thread(() -> {
                try {
                    while (totalConsumed.get() < producerCount * itemsPerProducer) {
                        String item = queue.poll(1, TimeUnit.SECONDS);
                        if (item != null) {
                            totalConsumed.incrementAndGet();
                            log.info("消费者{} 消费: {}, 队列大小: {}", 
                                consumerId, item, queue.size());
                            Thread.sleep(150);
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 等待所有线程完成
        latch.await();
        
        log.info("多生产者多消费者演示完成");
        log.info("总生产数量: {}, 总消费数量: {}, 队列剩余: {}", 
            totalProduced.get(), totalConsumed.get(), queue.size());
        
        // 性能统计
        log.info("生产者数量: {}, 消费者数量: {}", producerCount, consumerCount);
        log.info("平均每个生产者生产: {} 个", itemsPerProducer);
        log.info("平均每个消费者消费: {} 个", totalConsumed.get() / consumerCount);
    }
}
