package com.example.multithreading.benchmark;

import lombok.extern.slf4j.Slf4j;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 并发性能基准测试
 * 
 * 使用JMH (Java Microbenchmark Harness) 进行精确的性能测试
 * 
 * 测试内容：
 * 1. synchronized vs ReentrantLock
 * 2. AtomicInteger vs synchronized
 * 3. 不同并发集合性能
 * 4. 线程池性能
 * 
 * <AUTHOR> Team
 */
@Slf4j
@BenchmarkMode(Mode.Throughput)
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Warmup(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Fork(1)
@Threads(4)
public class ConcurrencyBenchmark {

    // 测试数据
    private volatile int volatileCounter = 0;
    private int normalCounter = 0;
    private AtomicInteger atomicCounter = new AtomicInteger(0);
    private final Object syncLock = new Object();
    private final ReentrantLock reentrantLock = new ReentrantLock();
    
    // 并发集合
    private ConcurrentHashMap<Integer, Integer> concurrentMap;
    private ConcurrentLinkedQueue<Integer> concurrentQueue;
    
    // 线程池
    private ExecutorService fixedThreadPool;
    private ExecutorService cachedThreadPool;
    private ForkJoinPool forkJoinPool;

    @Setup
    public void setup() {
        concurrentMap = new ConcurrentHashMap<>();
        concurrentQueue = new ConcurrentLinkedQueue<>();
        fixedThreadPool = Executors.newFixedThreadPool(4);
        cachedThreadPool = Executors.newCachedThreadPool();
        forkJoinPool = new ForkJoinPool(4);
        
        // 预填充数据
        for (int i = 0; i < 1000; i++) {
            concurrentMap.put(i, i);
            concurrentQueue.offer(i);
        }
    }

    @TearDown
    public void tearDown() {
        fixedThreadPool.shutdown();
        cachedThreadPool.shutdown();
        forkJoinPool.shutdown();
    }

    /**
     * 测试volatile变量性能
     */
    @Benchmark
    public int testVolatileIncrement() {
        return ++volatileCounter;
    }

    /**
     * 测试普通变量性能（非线程安全）
     */
    @Benchmark
    public int testNormalIncrement() {
        return ++normalCounter;
    }

    /**
     * 测试AtomicInteger性能
     */
    @Benchmark
    public int testAtomicIncrement() {
        return atomicCounter.incrementAndGet();
    }

    /**
     * 测试synchronized性能
     */
    @Benchmark
    public int testSynchronizedIncrement() {
        synchronized (syncLock) {
            return ++normalCounter;
        }
    }

    /**
     * 测试ReentrantLock性能
     */
    @Benchmark
    public int testReentrantLockIncrement() {
        reentrantLock.lock();
        try {
            return ++normalCounter;
        } finally {
            reentrantLock.unlock();
        }
    }

    /**
     * 测试ConcurrentHashMap读性能
     */
    @Benchmark
    public Integer testConcurrentMapRead() {
        return concurrentMap.get(ThreadLocalRandom.current().nextInt(1000));
    }

    /**
     * 测试ConcurrentHashMap写性能
     */
    @Benchmark
    public Integer testConcurrentMapWrite() {
        int key = ThreadLocalRandom.current().nextInt(1000);
        return concurrentMap.put(key, key);
    }

    /**
     * 测试ConcurrentLinkedQueue性能
     */
    @Benchmark
    public boolean testConcurrentQueueOffer() {
        return concurrentQueue.offer(ThreadLocalRandom.current().nextInt());
    }

    /**
     * 测试ConcurrentLinkedQueue性能
     */
    @Benchmark
    public Integer testConcurrentQueuePoll() {
        Integer result = concurrentQueue.poll();
        if (result != null) {
            concurrentQueue.offer(result); // 保持队列大小
        }
        return result;
    }

    /**
     * 主方法运行基准测试
     */
    public static void main(String[] args) throws RunnerException {
        log.info("=== 并发性能基准测试 ===");
        log.info("使用JMH进行精确的性能测试...");
        
        Options opt = new OptionsBuilder()
            .include(ConcurrencyBenchmark.class.getSimpleName())
            .build();
        
        new Runner(opt).run();
    }
}

/**
 * 简单的性能对比测试（不使用JMH）
 */
@Slf4j
class SimpleBenchmark {
    
    public static void main(String[] args) throws InterruptedException {
        log.info("=== 简单性能对比测试 ===");
        
        int threadCount = 4;
        int operationsPerThread = 1_000_000;
        
        // 测试不同同步机制的性能
        testSynchronizationPerformance(threadCount, operationsPerThread);
        
        // 测试不同集合的性能
        testCollectionPerformance(threadCount, operationsPerThread);
    }
    
    /**
     * 测试同步机制性能
     */
    private static void testSynchronizationPerformance(int threadCount, int operationsPerThread) 
            throws InterruptedException {
        log.info("--- 同步机制性能对比 ---");
        
        // AtomicInteger测试
        AtomicInteger atomicCounter = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        CountDownLatch atomicLatch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    atomicCounter.incrementAndGet();
                }
                atomicLatch.countDown();
            }).start();
        }
        atomicLatch.await();
        
        long atomicTime = System.currentTimeMillis() - startTime;
        
        // synchronized测试
        Counter syncCounter = new Counter();
        startTime = System.currentTimeMillis();
        
        CountDownLatch syncLatch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    syncCounter.increment();
                }
                syncLatch.countDown();
            }).start();
        }
        syncLatch.await();
        
        long syncTime = System.currentTimeMillis() - startTime;
        
        // ReentrantLock测试
        LockCounter lockCounter = new LockCounter();
        startTime = System.currentTimeMillis();
        
        CountDownLatch lockLatch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    lockCounter.increment();
                }
                lockLatch.countDown();
            }).start();
        }
        lockLatch.await();
        
        long lockTime = System.currentTimeMillis() - startTime;
        
        log.info("同步机制性能对比 ({} 线程, 每线程 {} 次操作):", threadCount, operationsPerThread);
        log.info("  AtomicInteger: {} ms", atomicTime);
        log.info("  synchronized: {} ms", syncTime);
        log.info("  ReentrantLock: {} ms", lockTime);
        log.info("  AtomicInteger vs synchronized: {:.2f}x", (double) syncTime / atomicTime);
        log.info("  AtomicInteger vs ReentrantLock: {:.2f}x", (double) lockTime / atomicTime);
    }
    
    /**
     * 测试集合性能
     */
    private static void testCollectionPerformance(int threadCount, int operationsPerThread) 
            throws InterruptedException {
        log.info("\n--- 集合性能对比 ---");
        
        // ConcurrentHashMap测试
        ConcurrentHashMap<Integer, Integer> concurrentMap = new ConcurrentHashMap<>();
        long startTime = System.currentTimeMillis();
        
        CountDownLatch mapLatch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    int key = threadId * operationsPerThread + j;
                    concurrentMap.put(key, key);
                }
                mapLatch.countDown();
            }).start();
        }
        mapLatch.await();
        
        long mapTime = System.currentTimeMillis() - startTime;
        
        // ConcurrentLinkedQueue测试
        ConcurrentLinkedQueue<Integer> concurrentQueue = new ConcurrentLinkedQueue<>();
        startTime = System.currentTimeMillis();
        
        CountDownLatch queueLatch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    concurrentQueue.offer(j);
                }
                queueLatch.countDown();
            }).start();
        }
        queueLatch.await();
        
        long queueTime = System.currentTimeMillis() - startTime;
        
        log.info("集合性能对比 ({} 线程, 每线程 {} 次操作):", threadCount, operationsPerThread);
        log.info("  ConcurrentHashMap: {} ms", mapTime);
        log.info("  ConcurrentLinkedQueue: {} ms", queueTime);
        log.info("  最终Map大小: {}", concurrentMap.size());
        log.info("  最终Queue大小: {}", concurrentQueue.size());
    }
    
    /**
     * synchronized计数器
     */
    static class Counter {
        private int count = 0;
        
        public synchronized void increment() {
            count++;
        }
        
        public synchronized int getCount() {
            return count;
        }
    }
    
    /**
     * ReentrantLock计数器
     */
    static class LockCounter {
        private int count = 0;
        private final ReentrantLock lock = new ReentrantLock();
        
        public void increment() {
            lock.lock();
            try {
                count++;
            } finally {
                lock.unlock();
            }
        }
        
        public int getCount() {
            lock.lock();
            try {
                return count;
            } finally {
                lock.unlock();
            }
        }
    }
}
