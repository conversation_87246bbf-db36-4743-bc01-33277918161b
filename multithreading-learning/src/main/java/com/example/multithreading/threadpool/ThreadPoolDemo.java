package com.example.multithreading.threadpool;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池演示
 * 
 * 知识点：
 * 1. Executor框架
 * 2. ThreadPoolExecutor详解
 * 3. 常用线程池类型
 * 4. 线程池参数配置
 * 5. 拒绝策略
 * 6. 线程池监控
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ThreadPoolDemo {

    public static void main(String[] args) throws InterruptedException, ExecutionException {
        log.info("=== 线程池演示 ===");
        
        // 1. 常用线程池类型
        demonstrateCommonThreadPools();
        
        // 2. 自定义线程池
        demonstrateCustomThreadPool();
        
        // 3. 线程池参数详解
        demonstrateThreadPoolParameters();
        
        // 4. 拒绝策略演示
        demonstrateRejectionPolicies();
        
        // 5. 线程池监控
        demonstrateThreadPoolMonitoring();
        
        // 6. 最佳实践
        demonstrateBestPractices();
    }

    /**
     * 常用线程池类型演示
     */
    private static void demonstrateCommonThreadPools() throws InterruptedException, ExecutionException {
        log.info("--- 1. 常用线程池类型演示 ---");
        
        // 1.1 FixedThreadPool - 固定大小线程池
        log.info("1.1 FixedThreadPool:");
        ExecutorService fixedPool = Executors.newFixedThreadPool(3);
        
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            fixedPool.submit(() -> {
                log.info("FixedThreadPool - 任务 {} 在线程 {} 中执行", 
                    taskId, Thread.currentThread().getName());
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        fixedPool.shutdown();
        fixedPool.awaitTermination(5, TimeUnit.SECONDS);
        
        // 1.2 CachedThreadPool - 缓存线程池
        log.info("\n1.2 CachedThreadPool:");
        ExecutorService cachedPool = Executors.newCachedThreadPool();
        
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            cachedPool.submit(() -> {
                log.info("CachedThreadPool - 任务 {} 在线程 {} 中执行", 
                    taskId, Thread.currentThread().getName());
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        cachedPool.shutdown();
        cachedPool.awaitTermination(3, TimeUnit.SECONDS);
        
        // 1.3 SingleThreadExecutor - 单线程执行器
        log.info("\n1.3 SingleThreadExecutor:");
        ExecutorService singlePool = Executors.newSingleThreadExecutor();
        
        for (int i = 1; i <= 3; i++) {
            final int taskId = i;
            singlePool.submit(() -> {
                log.info("SingleThreadExecutor - 任务 {} 在线程 {} 中执行", 
                    taskId, Thread.currentThread().getName());
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        singlePool.shutdown();
        singlePool.awaitTermination(3, TimeUnit.SECONDS);
        
        // 1.4 ScheduledThreadPool - 定时任务线程池
        log.info("\n1.4 ScheduledThreadPool:");
        ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(2);
        
        // 延迟执行
        scheduledPool.schedule(() -> {
            log.info("延迟1秒执行的任务");
        }, 1, TimeUnit.SECONDS);
        
        // 固定频率执行
        ScheduledFuture<?> future = scheduledPool.scheduleAtFixedRate(() -> {
            log.info("每2秒执行一次的任务");
        }, 0, 2, TimeUnit.SECONDS);
        
        // 运行6秒后停止
        Thread.sleep(6000);
        future.cancel(true);
        scheduledPool.shutdown();
        
        log.info("常用线程池类型演示完成\n");
    }

    /**
     * 自定义线程池演示
     */
    private static void demonstrateCustomThreadPool() throws InterruptedException {
        log.info("--- 2. 自定义线程池演示 ---");
        
        // 自定义线程工厂
        ThreadFactory customThreadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "CustomThread-" + threadNumber.getAndIncrement());
                thread.setDaemon(false);
                thread.setPriority(Thread.NORM_PRIORITY);
                return thread;
            }
        };
        
        // 创建自定义线程池
        ThreadPoolExecutor customPool = new ThreadPoolExecutor(
            2,                              // 核心线程数
            4,                              // 最大线程数
            60L,                            // 空闲线程存活时间
            TimeUnit.SECONDS,               // 时间单位
            new LinkedBlockingQueue<>(10),  // 工作队列
            customThreadFactory,            // 线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        
        // 提交任务
        for (int i = 1; i <= 8; i++) {
            final int taskId = i;
            customPool.submit(() -> {
                log.info("自定义线程池 - 任务 {} 在线程 {} 中执行", 
                    taskId, Thread.currentThread().getName());
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 监控线程池状态
        log.info("活跃线程数: {}", customPool.getActiveCount());
        log.info("池大小: {}", customPool.getPoolSize());
        log.info("队列大小: {}", customPool.getQueue().size());
        
        customPool.shutdown();
        customPool.awaitTermination(10, TimeUnit.SECONDS);
        
        log.info("自定义线程池演示完成\n");
    }

    /**
     * 线程池参数详解
     */
    private static void demonstrateThreadPoolParameters() throws InterruptedException {
        log.info("--- 3. 线程池参数详解 ---");
        
        // 创建一个参数明确的线程池
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
            1,                                  // corePoolSize: 核心线程数
            3,                                  // maximumPoolSize: 最大线程数
            30L,                                // keepAliveTime: 空闲线程存活时间
            TimeUnit.SECONDS,                   // unit: 时间单位
            new ArrayBlockingQueue<>(2),        // workQueue: 工作队列（容量为2）
            Executors.defaultThreadFactory(),   // threadFactory: 线程工厂
            new ThreadPoolExecutor.AbortPolicy() // handler: 拒绝策略
        );
        
        log.info("线程池参数:");
        log.info("  核心线程数: {}", pool.getCorePoolSize());
        log.info("  最大线程数: {}", pool.getMaximumPoolSize());
        log.info("  队列容量: {}", ((ArrayBlockingQueue<?>) pool.getQueue()).remainingCapacity() + pool.getQueue().size());
        
        // 提交任务观察线程池行为
        for (int i = 1; i <= 6; i++) {
            final int taskId = i;
            try {
                pool.submit(() -> {
                    log.info("任务 {} 开始执行，当前线程: {}", taskId, Thread.currentThread().getName());
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("任务 {} 执行完成", taskId);
                });
                
                log.info("提交任务 {} 后 - 活跃线程: {}, 池大小: {}, 队列大小: {}", 
                    taskId, pool.getActiveCount(), pool.getPoolSize(), pool.getQueue().size());
                
            } catch (RejectedExecutionException e) {
                log.warn("任务 {} 被拒绝: {}", taskId, e.getMessage());
            }
            
            Thread.sleep(500);
        }
        
        pool.shutdown();
        pool.awaitTermination(15, TimeUnit.SECONDS);
        
        log.info("线程池参数演示完成\n");
    }

    /**
     * 拒绝策略演示
     */
    private static void demonstrateRejectionPolicies() {
        log.info("--- 4. 拒绝策略演示 ---");
        
        // 4.1 AbortPolicy - 抛出异常
        log.info("4.1 AbortPolicy - 抛出异常:");
        demonstrateRejectionPolicy(new ThreadPoolExecutor.AbortPolicy(), "AbortPolicy");
        
        // 4.2 CallerRunsPolicy - 调用者执行
        log.info("\n4.2 CallerRunsPolicy - 调用者执行:");
        demonstrateRejectionPolicy(new ThreadPoolExecutor.CallerRunsPolicy(), "CallerRunsPolicy");
        
        // 4.3 DiscardPolicy - 静默丢弃
        log.info("\n4.3 DiscardPolicy - 静默丢弃:");
        demonstrateRejectionPolicy(new ThreadPoolExecutor.DiscardPolicy(), "DiscardPolicy");
        
        // 4.4 DiscardOldestPolicy - 丢弃最老的任务
        log.info("\n4.4 DiscardOldestPolicy - 丢弃最老的任务:");
        demonstrateRejectionPolicy(new ThreadPoolExecutor.DiscardOldestPolicy(), "DiscardOldestPolicy");
        
        log.info("拒绝策略演示完成\n");
    }

    private static void demonstrateRejectionPolicy(RejectedExecutionHandler handler, String policyName) {
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
            1, 1, 0L, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1),
            handler
        );
        
        // 提交3个任务，但线程池只能处理2个（1个执行，1个排队）
        for (int i = 1; i <= 3; i++) {
            final int taskId = i;
            try {
                pool.submit(() -> {
                    log.info("{} - 任务 {} 执行中", policyName, taskId);
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
                log.info("{} - 任务 {} 提交成功", policyName, taskId);
            } catch (RejectedExecutionException e) {
                log.warn("{} - 任务 {} 被拒绝", policyName, taskId);
            }
        }
        
        pool.shutdown();
        try {
            pool.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 线程池监控演示
     */
    private static void demonstrateThreadPoolMonitoring() throws InterruptedException {
        log.info("--- 5. 线程池监控演示 ---");
        
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(5),
            new CustomThreadFactory("Monitor"),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 启动监控线程
        Thread monitor = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                log.info("线程池监控 - 活跃线程: {}, 池大小: {}, 队列大小: {}, 已完成任务: {}", 
                    pool.getActiveCount(), 
                    pool.getPoolSize(), 
                    pool.getQueue().size(),
                    pool.getCompletedTaskCount());
                
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        
        monitor.start();
        
        // 提交任务
        for (int i = 1; i <= 10; i++) {
            final int taskId = i;
            pool.submit(() -> {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 监控5秒
        Thread.sleep(5000);
        monitor.interrupt();
        
        pool.shutdown();
        pool.awaitTermination(10, TimeUnit.SECONDS);
        
        log.info("线程池监控演示完成\n");
    }

    /**
     * 最佳实践演示
     */
    private static void demonstrateBestPractices() throws InterruptedException, ExecutionException {
        log.info("--- 6. 最佳实践演示 ---");
        
        // 6.1 合理设置线程池大小
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        int maximumPoolSize = corePoolSize * 2;
        
        log.info("CPU核心数: {}", corePoolSize);
        log.info("建议核心线程数: {}", corePoolSize);
        log.info("建议最大线程数: {}", maximumPoolSize);
        
        // 6.2 使用有界队列
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), // 有界队列
            new CustomThreadFactory("BestPractice"),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 6.3 提交Callable任务并处理结果
        Future<String> future = pool.submit(() -> {
            Thread.sleep(1000);
            return "任务执行结果";
        });
        
        try {
            String result = future.get(2, TimeUnit.SECONDS);
            log.info("任务结果: {}", result);
        } catch (TimeoutException e) {
            log.warn("任务执行超时");
            future.cancel(true);
        }
        
        // 6.4 优雅关闭线程池
        pool.shutdown();
        if (!pool.awaitTermination(5, TimeUnit.SECONDS)) {
            log.warn("线程池未能在指定时间内关闭，强制关闭");
            pool.shutdownNow();
        }
        
        log.info("最佳实践演示完成");
    }

    /**
     * 自定义线程工厂
     */
    static class CustomThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        
        public CustomThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-Thread-" + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
}
