package com.example.multithreading.async;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.List;
import java.util.Arrays;

/**
 * CompletableFuture异步编程演示
 * 
 * 知识点：
 * 1. CompletableFuture基本使用
 * 2. 异步任务链式调用
 * 3. 多个异步任务组合
 * 4. 异常处理
 * 5. 自定义线程池
 * 6. 实际应用场景
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class CompletableFutureDemo {

    private static final ExecutorService customExecutor = 
        Executors.newFixedThreadPool(4, r -> {
            Thread t = new Thread(r, "AsyncWorker-" + System.currentTimeMillis());
            t.setDaemon(false);
            return t;
        });

    public static void main(String[] args) throws InterruptedException, ExecutionException {
        log.info("=== CompletableFuture异步编程演示 ===");
        
        // 1. 基本使用
        demonstrateBasicUsage();
        
        // 2. 链式调用
        demonstrateChaining();
        
        // 3. 多任务组合
        demonstrateCombining();
        
        // 4. 异常处理
        demonstrateExceptionHandling();
        
        // 5. 实际应用场景
        demonstrateRealWorldScenario();
        
        // 关闭线程池
        customExecutor.shutdown();
        customExecutor.awaitTermination(5, TimeUnit.SECONDS);
    }

    /**
     * 基本使用演示
     */
    private static void demonstrateBasicUsage() throws InterruptedException, ExecutionException {
        log.info("--- 1. CompletableFuture基本使用 ---");
        
        // 1.1 创建已完成的Future
        CompletableFuture<String> completedFuture = CompletableFuture.completedFuture("Hello World");
        log.info("已完成的Future结果: {}", completedFuture.get());
        
        // 1.2 异步执行Supplier
        CompletableFuture<String> supplyAsync = CompletableFuture.supplyAsync(() -> {
            log.info("异步执行Supplier，线程: {}", Thread.currentThread().getName());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "异步计算结果";
        });
        
        log.info("SupplyAsync结果: {}", supplyAsync.get());
        
        // 1.3 异步执行Runnable
        CompletableFuture<Void> runAsync = CompletableFuture.runAsync(() -> {
            log.info("异步执行Runnable，线程: {}", Thread.currentThread().getName());
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            log.info("Runnable执行完成");
        });
        
        runAsync.get(); // 等待完成
        
        // 1.4 使用自定义线程池
        CompletableFuture<String> customPoolFuture = CompletableFuture.supplyAsync(() -> {
            log.info("使用自定义线程池，线程: {}", Thread.currentThread().getName());
            return "自定义线程池结果";
        }, customExecutor);
        
        log.info("自定义线程池结果: {}", customPoolFuture.get());
        
        log.info("基本使用演示完成\n");
    }

    /**
     * 链式调用演示
     */
    private static void demonstrateChaining() throws InterruptedException, ExecutionException {
        log.info("--- 2. 链式调用演示 ---");
        
        CompletableFuture<String> chainedFuture = CompletableFuture
            .supplyAsync(() -> {
                log.info("第一步：获取用户ID");
                return "user123";
            })
            .thenApply(userId -> {
                log.info("第二步：根据用户ID {} 获取用户信息", userId);
                return new User(userId, "张三", "<EMAIL>");
            })
            .thenApply(user -> {
                log.info("第三步：格式化用户信息 {}", user.getName());
                return String.format("用户：%s，邮箱：%s", user.getName(), user.getEmail());
            })
            .thenCompose(userInfo -> {
                log.info("第四步：异步获取用户权限");
                return CompletableFuture.supplyAsync(() -> {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    return userInfo + "，权限：管理员";
                });
            });
        
        log.info("链式调用最终结果: {}", chainedFuture.get());
        
        // thenAccept - 消费结果但不返回
        CompletableFuture<Void> acceptFuture = CompletableFuture
            .supplyAsync(() -> "处理数据")
            .thenAccept(result -> log.info("消费结果: {}", result));
        
        acceptFuture.get();
        
        // thenRun - 不接收参数也不返回结果
        CompletableFuture<Void> runFuture = CompletableFuture
            .supplyAsync(() -> "计算完成")
            .thenRun(() -> log.info("执行清理工作"));
        
        runFuture.get();
        
        log.info("链式调用演示完成\n");
    }

    /**
     * 多任务组合演示
     */
    private static void demonstrateCombining() throws InterruptedException, ExecutionException {
        log.info("--- 3. 多任务组合演示 ---");
        
        // 3.1 thenCombine - 两个任务都完成后组合结果
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
            log.info("任务1开始执行");
            sleep(1000);
            return "任务1结果";
        });
        
        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
            log.info("任务2开始执行");
            sleep(1500);
            return "任务2结果";
        });
        
        CompletableFuture<String> combinedFuture = future1.thenCombine(future2, 
            (result1, result2) -> {
                log.info("组合两个任务的结果");
                return result1 + " + " + result2;
            });
        
        log.info("组合结果: {}", combinedFuture.get());
        
        // 3.2 allOf - 等待所有任务完成
        CompletableFuture<String> task1 = CompletableFuture.supplyAsync(() -> {
            sleep(800);
            return "任务A";
        });
        
        CompletableFuture<String> task2 = CompletableFuture.supplyAsync(() -> {
            sleep(1200);
            return "任务B";
        });
        
        CompletableFuture<String> task3 = CompletableFuture.supplyAsync(() -> {
            sleep(600);
            return "任务C";
        });
        
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(task1, task2, task3);
        
        allTasks.thenRun(() -> {
            try {
                log.info("所有任务完成: {} {} {}", 
                    task1.get(), task2.get(), task3.get());
            } catch (Exception e) {
                log.error("获取任务结果失败", e);
            }
        }).get();
        
        // 3.3 anyOf - 任意一个任务完成
        CompletableFuture<String> fastTask1 = CompletableFuture.supplyAsync(() -> {
            sleep(500);
            return "快速任务1";
        });
        
        CompletableFuture<String> fastTask2 = CompletableFuture.supplyAsync(() -> {
            sleep(800);
            return "快速任务2";
        });
        
        CompletableFuture<Object> anyTask = CompletableFuture.anyOf(fastTask1, fastTask2);
        log.info("最先完成的任务结果: {}", anyTask.get());
        
        log.info("多任务组合演示完成\n");
    }

    /**
     * 异常处理演示
     */
    private static void demonstrateExceptionHandling() throws InterruptedException, ExecutionException {
        log.info("--- 4. 异常处理演示 ---");
        
        // 4.1 handle - 处理正常结果和异常
        CompletableFuture<String> handleFuture = CompletableFuture
            .supplyAsync(() -> {
                if (Math.random() > 0.5) {
                    throw new RuntimeException("随机异常");
                }
                return "正常结果";
            })
            .handle((result, throwable) -> {
                if (throwable != null) {
                    log.warn("处理异常: {}", throwable.getMessage());
                    return "异常处理后的默认值";
                } else {
                    log.info("处理正常结果: {}", result);
                    return result;
                }
            });
        
        log.info("Handle结果: {}", handleFuture.get());
        
        // 4.2 exceptionally - 只处理异常
        CompletableFuture<String> exceptionallyFuture = CompletableFuture
            .supplyAsync(() -> {
                throw new RuntimeException("模拟异常");
            })
            .exceptionally(throwable -> {
                log.warn("异常处理: {}", throwable.getMessage());
                return "异常恢复值";
            });
        
        log.info("Exceptionally结果: {}", exceptionallyFuture.get());
        
        // 4.3 whenComplete - 完成时回调（不改变结果）
        CompletableFuture<String> whenCompleteFuture = CompletableFuture
            .supplyAsync(() -> "正常完成")
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("任务异常完成", throwable);
                } else {
                    log.info("任务正常完成，结果: {}", result);
                }
            });
        
        log.info("WhenComplete结果: {}", whenCompleteFuture.get());
        
        log.info("异常处理演示完成\n");
    }

    /**
     * 实际应用场景演示
     */
    private static void demonstrateRealWorldScenario() throws InterruptedException, ExecutionException {
        log.info("--- 5. 实际应用场景演示 ---");
        
        // 模拟电商订单处理流程
        String orderId = "ORDER-12345";
        
        CompletableFuture<OrderInfo> orderProcessing = CompletableFuture
            // 1. 验证订单
            .supplyAsync(() -> {
                log.info("验证订单: {}", orderId);
                sleep(300);
                return new OrderInfo(orderId, "用户123", 299.99);
            })
            // 2. 检查库存
            .thenCompose(order -> {
                log.info("检查订单 {} 的库存", order.getOrderId());
                return CompletableFuture.supplyAsync(() -> {
                    sleep(500);
                    order.setStockAvailable(true);
                    return order;
                });
            })
            // 3. 计算价格
            .thenCompose(order -> {
                log.info("计算订单 {} 的最终价格", order.getOrderId());
                return CompletableFuture.supplyAsync(() -> {
                    sleep(200);
                    order.setFinalPrice(order.getAmount() * 0.9); // 9折优惠
                    return order;
                });
            })
            // 4. 处理支付
            .thenCompose(order -> {
                log.info("处理订单 {} 的支付", order.getOrderId());
                return CompletableFuture.supplyAsync(() -> {
                    sleep(800);
                    order.setPaid(true);
                    return order;
                });
            })
            // 5. 异常处理
            .handle((order, throwable) -> {
                if (throwable != null) {
                    log.error("订单处理失败: {}", throwable.getMessage());
                    return new OrderInfo(orderId, "ERROR", 0.0);
                }
                return order;
            });
        
        OrderInfo finalOrder = orderProcessing.get();
        log.info("订单处理完成: {}", finalOrder);
        
        // 并行处理多个服务调用
        log.info("并行调用多个服务...");
        
        CompletableFuture<String> userService = CompletableFuture.supplyAsync(() -> {
            log.info("调用用户服务");
            sleep(600);
            return "用户信息";
        });
        
        CompletableFuture<String> productService = CompletableFuture.supplyAsync(() -> {
            log.info("调用商品服务");
            sleep(800);
            return "商品信息";
        });
        
        CompletableFuture<String> inventoryService = CompletableFuture.supplyAsync(() -> {
            log.info("调用库存服务");
            sleep(400);
            return "库存信息";
        });
        
        CompletableFuture<String> aggregatedResult = userService
            .thenCombine(productService, (user, product) -> user + " + " + product)
            .thenCombine(inventoryService, (combined, inventory) -> combined + " + " + inventory);
        
        log.info("聚合服务调用结果: {}", aggregatedResult.get());
        
        log.info("实际应用场景演示完成");
    }

    /**
     * 工具方法：睡眠
     */
    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 用户类
     */
    static class User {
        private String id;
        private String name;
        private String email;
        
        public User(String id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }
        
        public String getId() { return id; }
        public String getName() { return name; }
        public String getEmail() { return email; }
    }

    /**
     * 订单信息类
     */
    static class OrderInfo {
        private String orderId;
        private String userId;
        private double amount;
        private double finalPrice;
        private boolean stockAvailable;
        private boolean paid;
        
        public OrderInfo(String orderId, String userId, double amount) {
            this.orderId = orderId;
            this.userId = userId;
            this.amount = amount;
        }
        
        // Getters and Setters
        public String getOrderId() { return orderId; }
        public String getUserId() { return userId; }
        public double getAmount() { return amount; }
        public double getFinalPrice() { return finalPrice; }
        public void setFinalPrice(double finalPrice) { this.finalPrice = finalPrice; }
        public boolean isStockAvailable() { return stockAvailable; }
        public void setStockAvailable(boolean stockAvailable) { this.stockAvailable = stockAvailable; }
        public boolean isPaid() { return paid; }
        public void setPaid(boolean paid) { this.paid = paid; }
        
        @Override
        public String toString() {
            return String.format("OrderInfo{orderId='%s', userId='%s', amount=%.2f, finalPrice=%.2f, stockAvailable=%s, paid=%s}",
                orderId, userId, amount, finalPrice, stockAvailable, paid);
        }
    }
}
