package com.example.multithreading.atomic;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.*;
import java.util.concurrent.CountDownLatch;

/**
 * 原子操作和CAS演示
 * 
 * 知识点：
 * 1. 原子类的使用
 * 2. CAS (Compare And Swap) 操作
 * 3. ABA问题及解决方案
 * 4. 原子类的性能对比
 * 5. 自定义原子操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class AtomicDemo {

    private static volatile int volatileCount = 0;
    private static int normalCount = 0;
    private static AtomicInteger atomicCount = new AtomicInteger(0);

    public static void main(String[] args) throws InterruptedException {
        log.info("=== 原子操作和CAS演示 ===");
        
        // 1. 基本原子类演示
        demonstrateBasicAtomicClasses();
        
        // 2. 原子类 vs synchronized 性能对比
        demonstratePerformanceComparison();
        
        // 3. CAS操作演示
        demonstrateCASOperations();
        
        // 4. ABA问题演示
        demonstrateABAProblem();
        
        // 5. 原子引用和字段更新器
        demonstrateAtomicReference();
        
        // 6. 原子数组
        demonstrateAtomicArray();
    }

    /**
     * 基本原子类演示
     */
    private static void demonstrateBasicAtomicClasses() throws InterruptedException {
        log.info("--- 1. 基本原子类演示 ---");
        
        // AtomicInteger
        AtomicInteger atomicInt = new AtomicInteger(0);
        log.info("AtomicInteger初始值: {}", atomicInt.get());
        
        // 基本操作
        log.info("incrementAndGet(): {}", atomicInt.incrementAndGet());
        log.info("getAndIncrement(): {}", atomicInt.getAndIncrement());
        log.info("addAndGet(5): {}", atomicInt.addAndGet(5));
        log.info("getAndAdd(3): {}", atomicInt.getAndAdd(3));
        log.info("当前值: {}", atomicInt.get());
        
        // AtomicLong
        AtomicLong atomicLong = new AtomicLong(1000L);
        log.info("AtomicLong操作: {} -> {}", 
            atomicLong.get(), atomicLong.updateAndGet(x -> x * 2));
        
        // AtomicBoolean
        AtomicBoolean atomicBoolean = new AtomicBoolean(false);
        log.info("AtomicBoolean compareAndSet: {} -> {}", 
            atomicBoolean.get(), atomicBoolean.compareAndSet(false, true));
        log.info("AtomicBoolean当前值: {}", atomicBoolean.get());
        
        // 多线程环境下的原子操作
        AtomicInteger counter = new AtomicInteger(0);
        int threadCount = 10;
        int incrementsPerThread = 1000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < incrementsPerThread; j++) {
                    counter.incrementAndGet();
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        log.info("多线程原子操作结果: 期望={}, 实际={}", 
            threadCount * incrementsPerThread, counter.get());
        
        log.info("基本原子类演示完成\n");
    }

    /**
     * 原子类 vs synchronized 性能对比
     */
    private static void demonstratePerformanceComparison() throws InterruptedException {
        log.info("--- 2. 性能对比演示 ---");
        
        int threadCount = 10;
        int operationsPerThread = 100000;
        
        // 测试普通变量（非线程安全）
        long startTime = System.currentTimeMillis();
        testNormalVariable(threadCount, operationsPerThread);
        long normalTime = System.currentTimeMillis() - startTime;
        
        // 测试volatile变量（非线程安全但可见性保证）
        startTime = System.currentTimeMillis();
        testVolatileVariable(threadCount, operationsPerThread);
        long volatileTime = System.currentTimeMillis() - startTime;
        
        // 测试AtomicInteger
        startTime = System.currentTimeMillis();
        testAtomicInteger(threadCount, operationsPerThread);
        long atomicTime = System.currentTimeMillis() - startTime;
        
        // 测试synchronized
        startTime = System.currentTimeMillis();
        testSynchronized(threadCount, operationsPerThread);
        long synchronizedTime = System.currentTimeMillis() - startTime;
        
        log.info("性能对比结果 ({} 线程, 每线程 {} 次操作):", threadCount, operationsPerThread);
        log.info("  普通变量: {} ms (结果可能不准确: {})", normalTime, normalCount);
        log.info("  volatile变量: {} ms (结果可能不准确: {})", volatileTime, volatileCount);
        log.info("  AtomicInteger: {} ms (结果: {})", atomicTime, atomicCount.get());
        log.info("  synchronized: {} ms", synchronizedTime);
        
        log.info("性能对比演示完成\n");
    }

    private static void testNormalVariable(int threadCount, int operationsPerThread) throws InterruptedException {
        normalCount = 0;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    normalCount++;
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    private static void testVolatileVariable(int threadCount, int operationsPerThread) throws InterruptedException {
        volatileCount = 0;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    volatileCount++;
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    private static void testAtomicInteger(int threadCount, int operationsPerThread) throws InterruptedException {
        atomicCount.set(0);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    atomicCount.incrementAndGet();
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    private static final Object syncLock = new Object();
    private static int syncCount = 0;

    private static void testSynchronized(int threadCount, int operationsPerThread) throws InterruptedException {
        syncCount = 0;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    synchronized (syncLock) {
                        syncCount++;
                    }
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    /**
     * CAS操作演示
     */
    private static void demonstrateCASOperations() {
        log.info("--- 3. CAS操作演示 ---");
        
        AtomicInteger value = new AtomicInteger(10);
        
        // 基本CAS操作
        log.info("初始值: {}", value.get());
        
        // 成功的CAS操作
        boolean success = value.compareAndSet(10, 20);
        log.info("CAS(10->20): 成功={}, 当前值={}", success, value.get());
        
        // 失败的CAS操作
        success = value.compareAndSet(10, 30);
        log.info("CAS(10->30): 成功={}, 当前值={}", success, value.get());
        
        // 使用CAS实现自旋锁
        demonstrateSpinLock();
        
        log.info("CAS操作演示完成\n");
    }

    private static void demonstrateSpinLock() {
        log.info("自旋锁演示:");
        SpinLock spinLock = new SpinLock();
        
        Thread[] threads = new Thread[3];
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                spinLock.lock();
                try {
                    log.info("线程 {} 获得自旋锁", threadId);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    spinLock.unlock();
                    log.info("线程 {} 释放自旋锁", threadId);
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 简单的自旋锁实现
     */
    static class SpinLock {
        private AtomicReference<Thread> owner = new AtomicReference<>();
        
        public void lock() {
            Thread currentThread = Thread.currentThread();
            while (!owner.compareAndSet(null, currentThread)) {
                // 自旋等待
            }
        }
        
        public void unlock() {
            Thread currentThread = Thread.currentThread();
            owner.compareAndSet(currentThread, null);
        }
    }

    /**
     * ABA问题演示
     */
    private static void demonstrateABAProblem() throws InterruptedException {
        log.info("--- 4. ABA问题演示 ---");
        
        // 使用AtomicStampedReference解决ABA问题
        AtomicStampedReference<Integer> stampedRef = new AtomicStampedReference<>(100, 0);
        
        Thread thread1 = new Thread(() -> {
            int stamp = stampedRef.getStamp();
            log.info("线程1读取值: {}, 版本: {}", stampedRef.getReference(), stamp);
            
            try {
                Thread.sleep(1000); // 模拟处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 尝试CAS操作
            boolean success = stampedRef.compareAndSet(100, 200, stamp, stamp + 1);
            log.info("线程1 CAS操作: 成功={}, 当前值={}, 当前版本={}", 
                success, stampedRef.getReference(), stampedRef.getStamp());
        });
        
        Thread thread2 = new Thread(() -> {
            try {
                Thread.sleep(500);
                
                // 修改值 100 -> 200
                int stamp = stampedRef.getStamp();
                stampedRef.compareAndSet(100, 200, stamp, stamp + 1);
                log.info("线程2修改: 100->200, 版本: {}", stampedRef.getStamp());
                
                // 再修改回来 200 -> 100
                stamp = stampedRef.getStamp();
                stampedRef.compareAndSet(200, 100, stamp, stamp + 1);
                log.info("线程2修改: 200->100, 版本: {}", stampedRef.getStamp());
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        thread1.start();
        thread2.start();
        
        thread1.join();
        thread2.join();
        
        log.info("ABA问题演示完成\n");
    }

    /**
     * 原子引用演示
     */
    private static void demonstrateAtomicReference() {
        log.info("--- 5. 原子引用演示 ---");
        
        // AtomicReference
        AtomicReference<String> atomicRef = new AtomicReference<>("初始值");
        log.info("AtomicReference初始值: {}", atomicRef.get());
        
        atomicRef.updateAndGet(s -> s + "-更新");
        log.info("更新后的值: {}", atomicRef.get());
        
        // AtomicMarkableReference
        AtomicMarkableReference<String> markableRef = 
            new AtomicMarkableReference<>("标记引用", false);
        
        boolean[] markHolder = new boolean[1];
        String value = markableRef.get(markHolder);
        log.info("AtomicMarkableReference值: {}, 标记: {}", value, markHolder[0]);
        
        markableRef.compareAndSet("标记引用", "新值", false, true);
        value = markableRef.get(markHolder);
        log.info("更新后值: {}, 标记: {}", value, markHolder[0]);
        
        log.info("原子引用演示完成\n");
    }

    /**
     * 原子数组演示
     */
    private static void demonstrateAtomicArray() throws InterruptedException {
        log.info("--- 6. 原子数组演示 ---");
        
        AtomicIntegerArray atomicArray = new AtomicIntegerArray(5);
        
        // 初始化数组
        for (int i = 0; i < atomicArray.length(); i++) {
            atomicArray.set(i, i * 10);
        }
        
        log.info("初始数组: {}", arrayToString(atomicArray));
        
        // 多线程修改数组
        CountDownLatch latch = new CountDownLatch(5);
        for (int i = 0; i < 5; i++) {
            final int index = i;
            new Thread(() -> {
                for (int j = 0; j < 100; j++) {
                    atomicArray.incrementAndGet(index);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        log.info("多线程修改后: {}", arrayToString(atomicArray));
        
        log.info("原子数组演示完成");
    }

    private static String arrayToString(AtomicIntegerArray array) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.length(); i++) {
            if (i > 0) sb.append(", ");
            sb.append(array.get(i));
        }
        sb.append("]");
        return sb.toString();
    }
}
