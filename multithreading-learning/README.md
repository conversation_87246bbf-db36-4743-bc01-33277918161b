# 🧵 Java多线程编程学习项目

## 📚 项目概述

这是一个完整的Java多线程编程学习项目，涵盖了从基础概念到高级应用的所有重要知识点。通过实际代码演示和详细注释，帮助您深入理解Java并发编程。

## 🎯 学习目标

- 掌握Java多线程的基本概念和创建方式
- 理解线程同步机制和并发控制
- 熟练使用线程池和并发工具类
- 了解原子操作和CAS机制
- 掌握并发集合的使用场景
- 学会分析和解决并发问题

## 📖 知识点结构

### 1. 基础线程操作 (`basic` 包)
- **ThreadCreationDemo**: 线程创建的4种方式
  - 继承Thread类
  - 实现Runnable接口
  - 实现Callable接口
  - 使用Lambda表达式
- **ThreadStateDemo**: 线程状态和生命周期
  - 6种线程状态详解
  - 状态转换过程
  - 状态监控实现

### 2. 同步机制 (`synchronization` 包)
- **SynchronizedDemo**: synchronized关键字
  - 同步方法 vs 同步代码块
  - 对象锁 vs 类锁
  - 死锁问题演示
- **LockDemo**: Lock接口详解
  - ReentrantLock可重入锁
  - ReadWriteLock读写锁
  - Condition条件变量
  - 公平锁 vs 非公平锁

### 3. 线程池 (`threadpool` 包)
- **ThreadPoolDemo**: 线程池完整演示
  - 常用线程池类型
  - ThreadPoolExecutor参数详解
  - 拒绝策略对比
  - 线程池监控
  - 最佳实践

### 4. 并发工具类 (`concurrent` 包)
- **ConcurrentUtilsDemo**: 并发工具类
  - CountDownLatch倒计时门闩
  - CyclicBarrier循环屏障
  - Semaphore信号量
  - Exchanger交换器
  - Phaser阶段器

### 5. 原子操作 (`atomic` 包)
- **AtomicDemo**: 原子类和CAS
  - 基本原子类使用
  - CAS操作原理
  - ABA问题及解决方案
  - 性能对比分析
  - 自旋锁实现

### 6. 并发集合 (`collections` 包)
- **ConcurrentCollectionsDemo**: 并发集合
  - ConcurrentHashMap并发哈希表
  - CopyOnWriteArrayList写时复制列表
  - ConcurrentLinkedQueue并发队列
  - BlockingQueue阻塞队列
  - ConcurrentSkipListMap并发跳表

### 7. 异步编程 (`async` 包)
- **CompletableFutureDemo**: CompletableFuture详解
  - 基本使用方法
  - 链式调用操作
  - 多任务组合
  - 异常处理机制
  - 实际应用场景

### 8. Fork/Join框架 (`forkjoin` 包)
- **ForkJoinDemo**: 分治算法演示
  - 数组求和并行计算
  - 快速排序并行实现
  - 斐波那契数列计算
  - 工作窃取算法
  - 性能对比分析

### 9. 设计模式 (`patterns` 包)
- **ProducerConsumerDemo**: 生产者消费者模式
  - BlockingQueue实现
  - wait/notify实现
  - Lock/Condition实现
  - 多生产者多消费者场景

### 10. 性能测试 (`benchmark` 包)
- **ConcurrencyBenchmark**: JMH性能基准测试
  - 同步机制性能对比
  - 并发集合性能测试
  - 精确的微基准测试
- **SimpleBenchmark**: 简单性能对比

### 11. Web演示 (`controller` 包)
- **DemoController**: Web接口演示
  - RESTful API接口
  - 异步Web响应
  - 实时性能测试

## 🚀 快速开始

### 运行环境要求
- JDK 17+
- Maven 3.6+

### 启动项目
```bash
# 编译项目
mvn clean compile

# 运行主程序
mvn spring-boot:run

# 或者运行具体的演示类
mvn exec:java -Dexec.mainClass="com.example.multithreading.basic.ThreadCreationDemo"
```

### 运行单个演示
每个演示类都可以独立运行：

```bash
# 线程创建演示
java com.example.multithreading.basic.ThreadCreationDemo

# 线程状态演示
java com.example.multithreading.basic.ThreadStateDemo

# 同步机制演示
java com.example.multithreading.synchronization.SynchronizedDemo

# 线程池演示
java com.example.multithreading.threadpool.ThreadPoolDemo

# 并发工具类演示
java com.example.multithreading.concurrent.ConcurrentUtilsDemo

# 原子操作演示
java com.example.multithreading.atomic.AtomicDemo

# 并发集合演示
java com.example.multithreading.collections.ConcurrentCollectionsDemo

# CompletableFuture异步编程演示
java com.example.multithreading.async.CompletableFutureDemo

# Fork/Join框架演示
java com.example.multithreading.forkjoin.ForkJoinDemo

# 生产者消费者模式演示
java com.example.multithreading.patterns.ProducerConsumerDemo

# 性能基准测试
java com.example.multithreading.benchmark.SimpleBenchmark
```

### Web接口演示
启动Spring Boot应用后，访问以下接口：

```bash
# 获取所有演示列表
curl http://localhost:8090/api/multithreading/demos

# 线程创建演示
curl http://localhost:8090/api/multithreading/basic/thread-creation

# 异步任务演示
curl http://localhost:8090/api/multithreading/async/completable-future

# 并行任务演示
curl http://localhost:8090/api/multithreading/async/parallel-tasks

# 性能基准测试
curl http://localhost:8090/api/multithreading/performance/benchmark
```

## 📊 学习路径建议

### 初学者路径
1. **基础概念** → `ThreadCreationDemo`
2. **线程状态** → `ThreadStateDemo`
3. **同步机制** → `SynchronizedDemo`
4. **线程池基础** → `ThreadPoolDemo`

### 进阶路径
1. **Lock机制** → `LockDemo`
2. **并发工具** → `ConcurrentUtilsDemo`
3. **原子操作** → `AtomicDemo`
4. **并发集合** → `ConcurrentCollectionsDemo`

## 🔍 重要概念解析

### 线程安全
- **定义**: 多个线程访问同一资源时，不会产生数据竞争
- **实现方式**: synchronized、Lock、原子类、不可变对象

### 死锁
- **产生条件**: 互斥、占有且等待、不可抢占、循环等待
- **避免策略**: 按序加锁、超时机制、死锁检测

### 性能考虑
- **锁的开销**: synchronized < ReentrantLock < 读写锁
- **上下文切换**: 线程数量不宜过多
- **内存可见性**: volatile关键字的使用

## 🛠️ 实际应用场景

### 生产者-消费者模式
- 使用BlockingQueue实现
- 适用于解耦生产和消费逻辑

### 线程池应用
- Web服务器请求处理
- 批量数据处理
- 定时任务执行

### 缓存实现
- 使用ConcurrentHashMap
- 读写锁优化读多写少场景

## 📈 性能测试

项目包含多种性能对比测试：
- synchronized vs Lock性能对比
- 原子类 vs synchronized性能对比
- 不同并发集合性能对比

## ⚠️ 常见陷阱

1. **忘记释放锁**: 使用try-finally确保锁释放
2. **死锁**: 避免嵌套锁，使用超时机制
3. **内存泄漏**: 及时关闭线程池
4. **上下文切换**: 合理设置线程数量
5. **可见性问题**: 正确使用volatile关键字

## 📚 扩展学习

- Java内存模型(JMM)
- Fork/Join框架
- CompletableFuture异步编程
- 响应式编程(RxJava)
- 分布式锁实现

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个学习项目！

## 📄 许可证

MIT License
