package com.example.common.model;

import lombok.Data;
import java.io.Serializable;

/**
 * 统一响应结果类
 * 用于所有模块的API响应格式统一
 *
 * @param <T> 响应数据类型
 * <AUTHOR> Team
 */
@Data
public class Result<T> implements Serializable {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 响应状态码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 私有构造函数，防止直接实例化
     */
    private Result() {}

    /**
     * 私有构造函数
     */
    private Result(boolean success, String code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 创建成功响应（带数据）
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(true, "200", "操作成功", data);
    }

    /**
     * 创建成功响应（无数据）
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>(true, "200", "操作成功", null);
    }

    /**
     * 创建成功响应（自定义消息）
     * @param message 成功消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(true, "200", message, data);
    }

    /**
     * 创建错误响应
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 错误响应
     */
    public static <T> Result<T> error(String code, String message) {
        return new Result<>(false, code, message, null);
    }

    /**
     * 创建错误响应（默认500错误码）
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 错误响应
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(false, "500", message, null);
    }

    /**
     * 判断是否成功
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 判断是否失败
     * @return true表示失败，false表示成功
     */
    public boolean isError() {
        return !success;
    }
}
