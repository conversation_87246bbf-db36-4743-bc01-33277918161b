<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
	<title>文字告白</title>
	<link rel="icon" href="https://cdn.jsdelivr.net/gh/wenoif/WebGift/love11/img/logo_32.ico" type="image/x-icon">
	<link rel="shortcut icon" href="https://cdn.jsdelivr.net/gh/wenoif/WebGift/love11/img/logo_32.ico" type="image/x-icon">
    <link rel="shortcut icon" type="image/x-icon" media="screen"/>
    <meta name="keywords" content=""/>
	<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description"
          content="有人说爱情可以让人失去生命，但我不会。我要留着这条命为你擦去嘴角的面渣；我要留着这条命去买你喜欢的玫瑰花；我要留着这条命拂去你眼角的泪水；我要留着这条命去撑起你的快乐天堂；我想和你一起过平淡的生活......清晨可以看到你的笑脸和餐桌上的牛奶煎蛋，然后一起出门，很努力地工作，下班去超市买菜，一起回家做晚饭，夕阳下牵手散步......一直到老..."/>
    <link href="css/style.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="js/js.js"></script>
</head>
<body id="bgc">
<script type="text/javascript" src="js/main.js"></script>
<div class="mb-box">
    <div class="bb">
        <h2>小燕子，我爱你</h2>
        <div class="bb_text">
            <div id="code">
                有人说爱情可以让人失去生命，但我不会。我要留着这条命为你擦去嘴角的面渣；我要留着这条命去买你喜欢的玫瑰花；我要留着这条命拂去你眼角的泪水；我要留着这条命去撑起你的快乐天堂；我想和你一起过平淡的生活......清晨可以看到你的笑脸和餐桌上的牛奶煎蛋，然后一起出门，很努力地工作，下班去超市买菜，一起回家做晚饭，夕阳下牵手散步......一直到老...
            </div>
        </div>
        <p class="bb_myname">
    </div>
</div>
<script>
    Element.prototype.typewriter = function (a) {
        var d = this,
            c = d.innerHTML,
            b = 0;
        d.innerHTML = "";
        var e = setInterval(function () {
            var f = c.substr(b, 1);
            if (f == "<") {
                b = c.indexOf(">", b) + 1
            } else {
                b++
            }
            d.innerHTML = c.substring(0, b) + (b & 1 ? "_" : "");
            if (b >= c.length) {
                clearInterval(e)
            }
        }, 150)
        return this

    }
    document.getElementById("code").typewriter();
</script>
<div style="display:none">
    <script type="text/javascript">
        var cpro_id = "u2782220";
    </script>
    <script type="text/javascript" src="js/c.js"></script>
</div>
<div style="display:none">
    <script type="text/javascript">
        /*677*250 创建于 Sun Oct 06 2017*/
        var cpro_id = "u2782229";
    </script>
    <script type="text/javascript" src="js/c.js"></script>
</div>
<link rel="stylesheet" href="css/cuplayer.css">
<div style="position:absolute; top:50px; right:50px;">
    <audio id="main_audio" autoplay="autoplay" preload="auto" loop>
        <source src="http://music.163.com/song/media/outer/url?id=1381755293.mp3" type="audio/mpeg"/>
    </audio>
    <a class="c-white fs-12 icon-play rotate" id="btn-play" href="javascript:void(0);"></a>
</div>

<script type="text/javascript">
    //获取picid函数
    $(function () {
        $("area").click(function () {
            var picId = $(this).attr("data-id");
            var picSrc = "https://cdn.jsdelivr.net/gh/wenoif/WebGift/love11/img/img_big/" + picId + ".jpg"
            $(".modal-content>.pic-box>img").attr("src", picSrc);
        })
        var isPlaying = function (audio) {
            return !audio.paused;
        }
        var a = document.getElementById('main_audio');
        if (!(a.play instanceof Function)) {
            a = document.getElementById('main_audio_ie8');
            isPlaying = function (audio) {
                return audio.playState == 2;
            }
        }
        $('#btn-play').on('click', function () {
            if ($(this).hasClass('rotate')) {
                a.pause();
                $(this).removeClass('rotate');
            } else {
                a.play();
                $(this).addClass('rotate');
            }
        });

    })
</script>


</body>
</html>
