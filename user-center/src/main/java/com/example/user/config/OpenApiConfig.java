package com.example.user.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI userCenterOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("用户中心 API")
                .description("""
                    ## 用户中心服务 API 文档

                    ### 功能概述
                    用户中心提供完整的用户管理功能，包括：
                    - 🔐 用户注册与登录
                    - 🎫 JWT Token 认证
                    - 👤 用户信息管理
                    - 🔒 权限控制

                    ### 认证说明
                    除了注册和登录接口外，其他接口都需要在请求头中携带 JWT Token：
                    ```
                    Authorization: Bearer {your-jwt-token}
                    ```

                    ### 响应格式
                    所有接口都使用统一的响应格式：
                    ```json
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {...}
                    }
                    ```

                    ### 错误码说明
                    - `200`: 操作成功
                    - `400`: 参数错误
                    - `401`: 认证失败
                    - `403`: 权限不足
                    - `404`: 资源不存在
                    - `500`: 服务器内部错误
                    """)
                .version("v1.0.0")
                .contact(new Contact()
                    .name("开发团队")
                    .email("<EMAIL>")
                    .url("https://example.com"))
                .license(new License()
                    .name("Apache 2.0")
                    .url("https://www.apache.org/licenses/LICENSE-2.0")))
            .servers(List.of(
                new Server()
                    .url("http://localhost:8084")
                    .description("本地开发环境"),
                new Server()
                    .url("https://api.example.com")
                    .description("生产环境")
            ))
            .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
            .components(new Components()
                .addSecuritySchemes("Bearer Authentication",
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .description("请输入 JWT Token，格式：Bearer {token}")
                )
            );
    }
}