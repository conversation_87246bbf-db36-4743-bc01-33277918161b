package com.example.user.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI userCenterOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("User Center API   nimama ")
                .description("用户中心服务 API 文档")
                .version("v1.0.0")
                .license(new License()
                    .name("Apache 2.011111")
                    .url("http://springdoc.org")));
    }
} 