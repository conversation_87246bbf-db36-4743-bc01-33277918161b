package com.example.user.exception;

import com.example.user.constant.UserConstants;
import lombok.Getter;

@Getter
public class UserException extends BaseException {
    public UserException(String errorCode, String message) {
        super(errorCode, message);
    }

    public static UserException userNotFound() {
        return new UserException(UserConstants.ERROR_USER_NOT_FOUND, MessageConstants.USER_NOT_FOUND);
    }

    public static UserException userExists() {
        return new UserException(UserConstants.ERROR_USER_EXISTS, MessageConstants.USERNAME_EXISTS);
    }

    public static UserException invalidPassword() {
        return new UserException(UserConstants.ERROR_INVALID_PASSWORD, MessageConstants.PASSWORD_INCORRECT);
    }

    public static UserException invalidToken() {
        return new UserException(UserConstants.ERROR_INVALID_TOKEN, MessageConstants.TOKEN_INVALID);
    }
}