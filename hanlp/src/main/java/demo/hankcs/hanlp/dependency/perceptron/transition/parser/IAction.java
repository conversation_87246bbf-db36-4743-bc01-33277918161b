/*
 * <author><PERSON> He</author>
 * <email><EMAIL></email>
 * <create-date>2018-05-04 上午10:23</create-date>
 *
 * <copyright file="IAction.java">
 * Copyright (c) 2018, <PERSON>. All Rights Reserved, http://www.hankcs.com/
 * This source is subject to <PERSON>. Please contact <PERSON> for more information.
 * </copyright>
 */
package demo.hankcs.hanlp.dependency.perceptron.transition.parser;

import demo.hankcs.hanlp.dependency.perceptron.transition.configuration.Configuration;

/**
 * <AUTHOR>
 */
public interface IAction
{
    void commit(int relation, float score, int relationSize, Configuration config);
}
