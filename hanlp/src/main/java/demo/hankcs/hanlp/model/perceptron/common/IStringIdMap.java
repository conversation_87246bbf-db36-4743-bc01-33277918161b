/*
 * <summary></summary>
 * <author><PERSON><PERSON></author>
 * <email><EMAIL></email>
 * <create-date>2016-09-04 PM4:39</create-date>
 *
 * <copyright file="LabelIdMap.java" company="码农场">
 * Copyright (c) 2008-2016, 码农场. All Right Reserved, http://www.hankcs.com/
 * This source is subject to Hankcs. Please contact <PERSON><PERSON> to get more information.
 * </copyright>
 */

package demo.hankcs.hanlp.model.perceptron.common;

public interface IStringIdMap
{
    int idOf(String string);
}
