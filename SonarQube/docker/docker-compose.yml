version: '3.7'

services:
  postgres:
    image: postgres:13
    restart: always
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - postgres_data:/var/lib/postgresql/data

  sonarqube:
    image: sonarqube:9.9-community
    restart: always
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
    depends_on:
      - postgres
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_bundled-plugins:/opt/sonarqube/bundled-plugins

volumes:
  postgres_data:
  sonarqube_data:
  sonarqube_extensions:
  sonarqube_bundled-plugins: