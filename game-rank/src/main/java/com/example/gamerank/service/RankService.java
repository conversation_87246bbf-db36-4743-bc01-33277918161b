package com.example.gamerank.service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class RankService {

    private static final String RANK_KEY = "game:rank";

    @Autowired
    private StringRedisTemplate redisTemplate;

    // 添加或更新玩家得分
    public void addScore(String playerName, double score) {
        redisTemplate.opsForZSet().add(RANK_KEY, playerName, score);
    }

    // 获取前N名排行榜（按分数降序）
    public List<Map<String, Object>> getTopPlayers(int count) {
        Set<ZSetOperations.TypedTuple<String>> set = redisTemplate.opsForZSet()
            .reverseRangeWithScores(RANK_KEY, 0, count - 1);

        List<Map<String, Object>> result = new ArrayList<>();
        if (set != null) {
            for (ZSetOperations.TypedTuple<String> tuple : set) {
                Map<String, Object> item = new HashMap<>();
                item.put("name", tuple.getValue());
                item.put("score", tuple.getScore());
                result.add(item);
            }
        }
        return result;
    }

    // 查询某个玩家的得分
    public Double getPlayerScore(String name) {
        return redisTemplate.opsForZSet().score(RANK_KEY, name);
    }

    // 删除玩家
    public Long removePlayer(String name) {
        return redisTemplate.opsForZSet().remove(RANK_KEY, name);
    }
}