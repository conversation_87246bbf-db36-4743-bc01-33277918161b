# about.md file

## About This Content

May 22, 2015

### License

The Eclipse Foundation makes available all content in this plug-in ("Content"). Unless otherwise indicated below, the
Content is provided to you under the terms and conditions of the Apache License, Version 2.0.  A copy of the Apache
License, Version 2.0 is available at
[http://www.apache.org/licenses/LICENSE-2.0.txt](http://www.apache.org/licenses/LICENSE-2.0.txt)

If you did not receive this Content directly from the Eclipse Foundation, the Content is being redistributed by another
party ("Redistributor") and different terms and conditions may apply to your use of any object code in the Content.
Check the Redistributor’s license that was provided with the Content. If no such license exists, contact the
Redistributor. Unless otherwise indicated below, the terms and conditions of the Apache License, Version 2.0 still apply
to any source code in the Content and such source code may be obtained at
[http://www.eclipse.org](http://www.eclipse.org).

# notice.md file

Note: the below Eclipse user agreement is standard.  It says "Unless otherwise indicated, "... before referring to the
EPL.  We indicate above that all content is licensed under the ASLv2 license.  -- <PERSON>

## Eclipse Foundation Software User Agreement

April 9, 2014

### Usage Of Content

THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR OTHER MATERIALS FOR OPEN SOURCE
PROJECTS (COLLECTIVELY "CONTENT"). USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS AGREEMENT AND/OR
THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW. BY USING THE CONTENT, YOU AGREE
THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE
AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW. IF YOU DO NOT AGREE TO THE TERMS AND CONDITIONS OF THIS AGREEMENT
AND THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW, THEN YOU MAY
NOT USE THE CONTENT.

### Applicable Licenses

Unless otherwise indicated, all Content made available by the Eclipse Foundation is provided to you under the terms and
conditions of the Eclipse Public License Version 1.0 ("EPL"). A copy of the EPL is provided with this Content and is
also available at [http://www.eclipse.org/legal/epl-v10.html](http://www.eclipse.org/legal/epl-v10.html). For purposes
of the EPL, "Program" will mean the Content.

Content includes, but is not limited to, source code, object code, documentation and other files maintained in the
Eclipse Foundation source code repository ("Repository") in software modules ("Modules") and made available as
downloadable archives ("Downloads").

*   Content may be structured and packaged into modules to facilitate delivering, extending, and upgrading the Content.
    Typical modules may include plug-ins ("Plug-ins"), plug-in fragments ("Fragments"), and features ("Features").
*   Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java™ ARchive) in a directory named "plugins".
*   A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material. Each Feature may be packaged
    as a sub-directory in a directory named "features". Within a Feature, files named "feature.xml" may contain a list
    of the names and version numbers of the Plug-ins and/or Fragments associated with that Feature.
*   Features may also include other Features ("Included Features"). Within a Feature, files named "feature.xml" may
    contain a list of the names and version numbers of Included Features.

The terms and conditions governing Plug-ins and Fragments should be contained in files named "about.html" ("Abouts").
The terms and conditions governing Features and Included Features should be contained in files named "license.html"
("Feature Licenses"). Abouts and Feature Licenses may be located in any directory of a Download or Module including, but
not limited to the following locations:

*   The top-level (root) directory
*   Plug-in and Fragment directories
*   Inside Plug-ins and Fragments packaged as JARs
*   Sub-directories of the directory named "src" of certain Plug-ins
*   Feature directories

Note: if a Feature made available by the Eclipse Foundation is installed using the Provisioning Technology (as defined
below), you must agree to a license ("Feature Update License") during the installation process. If the Feature contains
Included Features, the Feature Update License should either provide you with the terms and conditions governing the
Included Features or inform you where you can locate them. Feature Update Licenses may be found in the "license"
property of files named "feature.properties" found within a Feature. Such Abouts, Feature Licenses, and Feature Update
Licenses contain the terms and conditions (or references to such terms and conditions) that govern your use of the
associated Content in that directory.

THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR
TERMS AND CONDITIONS. SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):

*   Eclipse Distribution License Version 1.0 (available at
    [http://www.eclipse.org/licenses/edl-v1.0.html](http://www.eclipse.org/licenses/edl-v10.html))
*   Common Public License Version 1.0 (available at
    [http://www.eclipse.org/legal/cpl-v10.html](http://www.eclipse.org/legal/cpl-v10.html))
*   Apache Software License 1.1 (available at
    [http://www.apache.org/licenses/LICENSE](http://www.apache.org/licenses/LICENSE))
*   Apache Software License 2.0 (available at
    [http://www.apache.org/licenses/LICENSE-2.0](http://www.apache.org/licenses/LICENSE-2.0))
*   Mozilla Public License Version 1.1 (available at
    [http://www.mozilla.org/MPL/MPL-1.1.html](http://www.mozilla.org/MPL/MPL-1.1.html))

IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR TO USE OF THE CONTENT. If no About, Feature
License, or Feature Update License is provided, please contact the Eclipse Foundation to determine what terms and
conditions govern that particular Content.

### Use of Provisioning Technology

The Eclipse Foundation makes available provisioning software, examples of which include, but are not limited to, p2 and
the Eclipse Update Manager ("Provisioning Technology") for the purpose of allowing users to install software,
documentation, information and/or other materials (collectively "Installable Software"). This capability is provided
with the intent of allowing such users to install, extend and update Eclipse-based products. Information about packaging
Installable Software is available at
[http://eclipse.org/equinox/p2/repository_packaging.html](http://eclipse.org/equinox/p2/repository_packaging.html)
("Specification").

You may use Provisioning Technology to allow other parties to install Installable Software. You shall be responsible for
enabling the applicable license agreements relating to the Installable Software to be presented to, and accepted by, the
users of the Provisioning Technology in accordance with the Specification. By using Provisioning Technology in such a
manner and making it available in accordance with the Specification, you further acknowledge your agreement to, and the
acquisition of all necessary rights to permit the following:

1.  A series of actions may occur ("Provisioning Process") in which a user may execute the Provisioning Technology on a
    machine ("Target Machine") with the intent of installing, extending or updating the functionality of an
    Eclipse-based product.
2.  During the Provisioning Process, the Provisioning Technology may cause third party Installable Software or a portion
    thereof to be accessed and copied to the Target Machine.
3.  Pursuant to the Specification, you will provide to the user the terms and conditions that govern the use of the
    Installable Software ("Installable Software Agreement") and such Installable Software Agreement shall be accessed
    from the Target Machine in accordance with the Specification. Such Installable Software Agreement must inform the
    user of the terms and conditions that govern the Installable Software and must solicit acceptance by the end user in
    the manner prescribed in such Installable Software Agreement. Upon such indication of agreement by the user, the
    provisioning Technology will complete installation of the Installable Software.

### Cryptography

Content may contain encryption software. The country in which you are currently may have restrictions on the import,
possession, and use, and/or re-export to another country, of encryption software. BEFORE using any encryption software,
please check the country's laws, regulations and policies concerning the import, possession, or use, and re-export of
encryption software, to see if this is permitted.

<small>Java and all Java-based trademarks are trademarks of Oracle Corporation in the United States, other countries,
or both.</small>
