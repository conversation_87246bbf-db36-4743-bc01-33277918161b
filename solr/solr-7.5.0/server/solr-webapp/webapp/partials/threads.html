<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<div id="threads" ng-class="showAllStacktraces ? 'expanded' : 'collapsed'">

  <div class="controls">
    <a ng-click="toggleStacktraces()">
      <span>{{showAllStacktraces?"Hide":"Show"}} all Stacktraces</span>
    </a>
  </div>

  <div id="thread-dump">

    <table border="0" cellpadding="0" cellspacing="0">

      <thead>

        <tr>

          <th class="name">name</th>
          <th class="time">cpuTime / userTime</th>

        </tr>

      </thead>

      <tbody>
        <tr ng-repeat="thread in threads" class="{{$odd?'odd':''}} {{thread.lock?'lock':''}} {{thread.stackTrace?'stacktrace':''}} {{thread.state}}">
          <td class="name">
            <a title="{{thread.state}}" ng-click="toggleStacktrace(thread)"><span>{{thread.name}} ({{thread.id}})</span></a>
            <p title="Waiting on" ng-show="thread.lock">{{thread.lock}}</p>
            <div ng-show="thread.showStackTrace">
              <ul>
                <li ng-repeat="trace in thread.stackTrace track by trace.id">{{trace.trace}}</li>
              </ul>
            </div>
          </td>
          <td class="time">{{thread.cpuTime}}<br>{{thread.userTime}}</td>
        </tr>
      </tbody>

    </table>

  </div>

  <div class="controls">
    <a ng-click="toggleStacktraces()">
      <span>{{showAllStacktraces?"Hide":"Show"}} all Stacktraces</span>
    </a>
  </div>

</div>
