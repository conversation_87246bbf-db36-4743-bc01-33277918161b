<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<div id="analysis">

  <div class="block analysis-error" id="analysis-handler-missing" ng-show="isHandlerMissing">
    <div class="head">This Functionality requires the <code>/analysis/field</code> Handler to be registered and active!</div>
  </div>

  <div class="block analysis-error" id="analysis-error" ng-show="analysisError">
    <div class="body">{{analysisError}}</div>
  </div>

  <div id="analysis-holder">

    <div id="field-analysis">

      <form method="get">

        <ul class="clearfix">

          <li class="index">

            <label for="analysis_fieldvalue_index">Field Value (Index)</label>
            <textarea name="analysis.fieldvalue" id="analysis_fieldvalue_index" ng-model="indexText"></textarea>

          </li>

          <li class="query">

            <label for="analysis_fieldvalue_query">Field Value (Query)</label>
            <textarea name="analysis.query" id="analysis_fieldvalue_query" ng-model="queryText"></textarea>

          </li>

          <li class="settings-holder clearfix">
            <div class="settings clearfix">
              <label for="type_or_name">Analyse Fieldname / FieldType:</label>
              <select style="width:130px" chosen ng-change="changeFieldOrType()" id="type_or_name" ng-model="fieldOrType" ng-options="f.value as f.label group by f.group for f in fieldsAndTypes"></select>

              <a id="tor_schema" ng-href="#/{{core}}/schema?{{schemaBrowserUrl}}"><span>Schema Browser</span>&nbsp;</a>

              <div class="buttons clearfix">

                <button type="submit" ng-click="updateQueryString()"><span>Analyse Values</span></button>

                <div class="verbose_output" ng-class="{active:verbose}">
                  <a ng-click="toggleVerbose()">Verbose Output</a>
                </div>

              </div>

            </div>
          </li>

        </ul>

      </form>

    </div>

    <div id="analysis-result" class="clearfix verbose_output">
      <div ng-class="key" ng-repeat="(key, type) in result">
        <table border="0" cellspacing="0" cellpadding="0">
          <tbody ng-repeat="component in type">
            <tr class="step">
              <td class="part analyzer">
                <div>
                  <abbr title="{{component.name}}">{{component.short}}</abbr>
                </div>
              </td>

              <td class="part legend" ng-show="verbose">
                <div class="holder">
                  <table border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td>
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tr ng-repeat="caption in component.captions" class="{{generate_class_name( short_key )}}">
                            <td>{{ caption }}</td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </div>
              </td>
              <td class="part data" ng-class="{spacer:token.blank}" colspan="1" ng-repeat="token in component.tokens track by token.index">
                <div class="holder" ng-hide="token.blank">
                  <table border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                      <tr class="details">
                        <td class="details">
                          <table border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                              <tr class="{{value.name}} {{value.extraclass}}" ng-repeat="value in token.keys" ng-show="verbose || value.name=='text'">
                                <td>{{value.value}}</td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="holder" ng-show="token.blank">&nbsp;</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

</div>
