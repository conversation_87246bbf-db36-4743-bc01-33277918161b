/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #cloud
{
  position: relative;
}

#content #cloud .loader
{
  background-position: 0 50%;
  padding-left: 21px;
}

#content #cloud #error
{
  background-color: #f00;
  background-image: url( ../../img/ico/construction.png );
  background-position: 10px 12px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 10px;
  padding-left: 35px;
}

#content #cloud #error .msg
{
  font-style: italic;
  font-weight: normal;
  margin-top: 10px;
}

#content #cloud #debug
{
  background-color: #fff;
  box-shadow: 0px 0px 10px #c0c0c0;
  -moz-box-shadow: 0px 0px 10px #c0c0c0;
  -webkit-box-shadow: 0px 0px 10px #c0c0c0;
  padding: 20px;
  position: absolute;
  left: 50px;
  top: 10px;
}

#content #cloud #debug ul
{
  margin-bottom: 5px;
}

#content #cloud #debug ul a
{
  background-position: 4px 50%;
  border-right: 0;
  display: block;
  padding: 2px 4px;
  padding-left: 25px;
}

#content #cloud #debug ul a:hover,
#content #cloud #debug ul a.hover
{
  background-color: #f0f0f0;
}

#content #cloud #debug .clipboard
{
  float: left;
  position: relative;
}

#content #cloud #debug .clipboard a
{
  background-image: url( ../../img/ico/clipboard-paste.png );
  z-index: 98;
}

#content #cloud #debug .clipboard a:hover,
#content #cloud #debug .clipboard a.hover,
#content #cloud #debug .clipboard.copied a
{
  background-image: url( ../../img/ico/clipboard-paste-document-text.png );
}

#content #cloud #debug .close
{
  float: right;
}

#content #cloud #debug .close a
{
  background-image: url( ../../img/ico/cross-0.png );
  padding-left: 21px;
}

#content #cloud #debug .close a:hover
{
  background-image: url( ../../img/ico/cross-1.png );
}

#content #cloud #debug .debug
{
  border: 1px solid #f0f0f0;
  max-height: 350px;
  overflow: auto;
  padding: 5px;
  width: 500px;
}

#content #cloud #debug .debug .loader
{
  background-position: 5px 50%;
  display: block;
  padding: 10px 26px;
}

#content #cloud .content
{
  padding-left: 0;
  padding-right: 0;
}

#content #cloud .content.show
{
  background-image: url( ../../img/div.gif );
  background-repeat: repeat-y;
  background-position: 31% 0;
}

#content #cloud #tree
{
  float: left;
  width: 30%;
}

#content #cloud .show #tree
{
  overflow: hidden;
}

#content #cloud #file-content
{
  float: right;
  position: relative;
  width: 68%;
  min-height: 100px
}

#content #cloud .show #file-content
{
  display: block;
}

#content #cloud #file-content .close
{
  background-image: url( ../../img/ico/cross-0.png );
  background-position: 50% 50%;
  display: block;
  height: 20px;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
}

#content #cloud #file-content .close:hover
{
  background-image: url( ../../img/ico/cross-1.png );
}

#content #cloud #file-content #toggle.plus
{
  font-style: italic;
  padding-left: 17px;
  background-image: url( ../../img/ico/toggle-small-expand.png );
}

#content #cloud #file-content #toggle.minus
{
  font-style: italic;
  padding-left: 17px;
  background-image: url( ../../img/ico/toggle-small.png );
}

#content #cloud #file-content #data
{
  border-top: 1px solid #c0c0c0;
  margin-top: 10px;
  padding-top: 10px;
}

#content #cloud #file-content #data pre
{
  display: block;
  max-height: 600px;
  overflow: auto;
}

#content #cloud #file-content #data em
{
  color: #4d4d4d;
}

#content #cloud #file-content #prop
{
}

#content #cloud #file-content li
{
  padding-top: 3px;
  padding-bottom: 3px;
}

#content #cloud #file-content li.odd
{
  background-color: #F8F8F8;
}

#content #cloud #file-content li dt
{
  float: left;
  width: 19%;
}

#content #cloud #file-content li dd
{
  float: right;
  width: 80%;
}

/* tree */

#content #cloud #legend
{
  border: 1px solid #f0f0f0;
  padding: 10px;
  position: absolute;
  right: 0;
  bottom: 0;
}

#content #cloud #legend li
{
  padding-left: 15px;
  position: relative;
}

#content #cloud #legend li svg
{
  position: absolute;
  left: 0;
  top: 2px;
}

#content #graph-content
{
  min-height: 400px;
}

#content #graph-content .node
{
  fill: #333;
}

#content #cloud #legend circle,
#content #graph-content .node circle
{
  fill: #fff;
  stroke: #c0c0c0;
  stroke-width: 1.5px;
}

#content #graph-content .node.lvl-3 text
{
  cursor: pointer;
}

#content #graph-content .node.lvl-3:hover circle
{
  stroke: #000 !important;
}

#content #graph-content .node.lvl-3:hover text
{
  fill: #000 !important;
}

#content #graph-content .link
{
  fill: none;
  stroke: #e0e0e0;
  stroke-width: 1.5px;
}

#content #cloud #legend .gone circle,
#content #graph-content .node.gone circle,
#content #graph-content .link.gone
{
  stroke: #f0f0f0;
}

#content #graph-content .node.gone text
{
  fill: #f0f0f0;
}

#content #cloud #legend ul .gone
{
  color: #e0e0e0;
}

#content #cloud #legend .recovery_failed,
#content #cloud #legend .recovery_failed circle,
#content #graph-content .node.recovery_failed circle
{
  color: #C43C35;
  stroke: #C43C35;
  font-style: italic;
}

#content #graph-content .node.recovery_failed text
{
  fill: #C43C35;
  font-style: italic;
}

#content #cloud #legend .down,
#content #cloud #legend .down circle,
#content #graph-content .node.down circle
{
  color: #c48f00;
  stroke: #c48f00;
}

#content #graph-content .node.down text
{
  fill: #c48f00;
}

#content #cloud #legend .recovering,
#content #cloud #legend .recovering circle,
#content #graph-content .node.recovering circle
{
  color: #d5dd00;
  stroke: #d5dd00;
  font-style: italic;
}

#content #graph-content .node.recovering text
{
  fill: #d5dd00;
  font-style: italic;
}

#content #cloud #legend .active,
#content #cloud #legend .active circle,
#content #graph-content .node.active circle
{
  color: #57A957;
  stroke: #57A957;
}

#content #graph-content .node.active text
{
  fill: #57A957;
}

#content #cloud #legend .leader circle,
#content #graph-content .node.leader circle
{
  fill: #000;
}

#content #cloud #legend .leader circle
{
  stroke: #fff;
}

#content #graph-content .link.lvl-2,
#content #graph-content .link.leader
{
  stroke: #c0c0c0;
}

#content #cloud #legend .leader,
#content #graph-content .leader text
{
  font-weight: bold;
}

#content #graph-content .node.lvl-0 circle
{
  stroke: #fff;
}

#content #graph-content .link.lvl-1
{
  stroke: #fff;
}

#cloudGraphPaging
{
  display: inline-block;
  padding-top: 15px;
  padding-bottom: 15px;
}

#nodesPaging
{
  padding-top: 5px;
  padding-bottom: 5px;
}

#content #cloud #legend .shard-inactive,
#content #cloud #legend .shard-inactive li,
#content #cloud #legend .shard-inactive li text,
#content #graph-content .shard-inactive text
{
  text-decoration: line-through;
}
#content #cloud #legend .shard-inactive circle,
#content #graph-content .shard-inactive circle,
#content #graph-content .link.shard-inactive
{
  stroke: #e9e9e9;
}

#content #cloud #legend .replicatype,
#content #cloud #legend .replicatype rect,
#content #graph-content .node.replicatype rect
{
  color: #007BA7;
  stroke: #007BA7;
  fill:rgb(0,123,167);

}

#content #graph-content .node.replicatype text
{
  fill: #007BA7;
}

/* Nodes tab */
#nodes-table {
  border-collapse: collapse;
}

#nodes-table td, #nodes-table th {
  border: 1px solid #ddd;
  padding: 8px;
  vertical-align: top;
}
#nodes-table th {
  font-weight: bolder;
  font-stretch: extra-expanded;
  background: #F8F8F8;
}
#content #cloud #nodes-content #nodes-table
{
  border-top: 1px solid #c0c0c0;
  margin-top: 10px;
  padding-top: 10px;
}

#content #cloud #nodes-content .host-name,
#content #cloud #nodes-content .node-name a
{
  font-weight: bold;
  font-size: larger;
}

#content #cloud #nodes-content a,
#content #cloud #nodes-content a:hover,
#content #cloud #nodes-content a.hover
{
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-color: #beebff;
}

#content #cloud #nodes-content a:hover,
#content #cloud #nodes-content a.hover
{
  background-color: #beebff;
}

#content #cloud #nodes-content .host-spec,
#content #cloud #nodes-content .node-spec,
#content #cloud #nodes-content .node-spec a
{
  font-style: italic;
}
#content #cloud #nodes-content .node-uptime
{
  font-weight: bolder;
  font-size: 20px;
}
#content #cloud #nodes-content .node-load,
#content #cloud #nodes-content .node-cpu,
#content #cloud #nodes-content .node-heap,
#content #cloud #nodes-content .node-disk
{
  font-weight: bolder;
  font-size: 20px;
}

#content #cloud #nodes-content .pct-normal
{
  color: darkgreen;
}

#content #cloud #nodes-content .pct-warn
{
  color: orange;
}

#content #cloud #nodes-content .pct-critical
{
  color: red;
}

/* Styling of reload and details buttons */
#content #cloud #controls,
#content #cloud #frame #zk-status-content #zk-controls
{
  display: block;
  height: 30px;
}

#content #cloud .reload
{
  background-image: url( ../../img/ico/arrow-circle.png );
  padding-left: 21px;
  float: left;
}

#content #cloud .reload.loader
{
  padding-left: 0;
}

#content #cloud .details-button 
{
  background-image: url(../../img/ico/ui-check-box-uncheck.png);
  background-position: 0 50%;
  color: #8D8D8D;
  margin-top: 7px;
  margin-left: 10px;
  padding-left: 21px;
  width: 30px;
}

#content #cloud .details-button.on
{
  background-image: url( ../../img/ico/ui-check-box.png );
  color: #333;
}

#content #cloud #nodes-content .more
{
  font-style: italic;
  text-underline: #0000fa;
}

/* Disk usage details d3 chart bars style */
.chart {
    background: #eee;
    padding: 1px;
}
.chart div {
    width:90%;
}
.chart div div {
    display:inline-block;
}
.chart div div.rect {
    transition: all 0.5s ease-out;
    -moz-transition: all 0.5s ease-out;
    -webkit-transition: all 0.5s ease-out;
    width:0;
    font: 10px sans-serif;
    background-color: #4CAF50;
    text-align: left;
    padding: 3px;
    margin: 2px;
    color: #000000;
    box-shadow: 1px 1px 1px #666;
}

#content #nodes-content .leader
{
  font-weight: bold;
}

#content #nodes-content .scroll-height-250
{
  max-height: 250px;
  overflow-scrolling: auto;
  overflow: auto;
  /*overflow-y: auto;*/
}

#content #nodes-content .min-width-150
{
  min-width: 150px;
}

#content #cloud #nodes-content .node-cores
{
  min-width: 150px;
}

#content #nodes-content .core-details
{
  padding-left: 21px;
}



::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 7px;
}

::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(0,0,0,.5);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
}
#content #cloud #zk-table td,
#content #cloud #zk-table th 
{
  border: 0px solid #ddd;
  border-bottom: 0.50px solid #eee;
  padding-right: 5px;
  padding-left: 5px;
}

#content #cloud #zk-table th 
{
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
  font-weight: bolder;
  font-stretch: extra-expanded;
  background: #F8F8F8;
}

#content #cloud #zk-table
{
  border-top: 1px solid #c0c0c0;
  margin-top: 10px;
  border-collapse: collapse;

  font-weight: bold;
}

#content #cloud #zk-table #detail-divider 
{
  background-color: #f8f8f8;
  height: 10px;
}

.zookeeper-status
{
  font-size: large;
}

.zookeeper-errors
{
  background-color: lightpink;
  padding: 10px;
  border: 1px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.zookeeper-errors li::before 
{
  content: "- ";
}

.zkstatus-green
{
  color: darkgreen;
}

.zkstatus-yellow
{
  color: orange;
}

.zkstatus-red
{
  color: red;
}
