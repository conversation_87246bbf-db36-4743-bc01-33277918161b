/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #analysis-holder
{
  background-image: url( ../../img/div.gif );
  background-position: 50% 0;
  background-repeat: repeat-y;
}

#content #analysis #field-analysis
{
  margin-bottom: 0;
}

#content #analysis #field-analysis .content
{
  padding-bottom: 0;
}

#content #analysis .settings-holder
{
  clear: both;
  padding-top: 15px;
}

#content #analysis .settings
{
  background-color: #fff;
  border-top: 1px solid #fafafa;
  border-bottom: 1px solid #fafafa;
  padding-top: 10px;
  padding-bottom: 10px;
}

#content #analysis .settings select.loader
{
  background-position: 3px 50%;
  padding-left: 21px;
}

#content #analysis .settings select optgroup
{
  font-style: normal;
  padding: 5px;
}

#content #analysis .settings select option
{
  padding-left: 10px;
}

#content #analysis .settings #tor_schema
{
  background-image: url( ../../img/ico/question-white.png );
  background-position: 0 50%;
  color: #4D4D4D;
  margin-left: 5px;
  padding-left: 21px;
}

#content #analysis .settings #tor_schema:hover
{
  background-image: url( ../../img/ico/question.png );
}

#content #analysis .settings #tor_schema span
{
//  display: none;
}

#content #analysis .settings #tor_schema:hover span
{
  display: inline;
}

#content #analysis .settings .buttons
{
  float: right;
  width: 47%;
}

#content #analysis .settings button
{
  float: right;
}

#content #analysis .settings button span
{
  background-image: url( ../../img/ico/funnel.png );
}

#content #analysis .settings .verbose_output
{
  float: left;
  width: auto;
}

#content #analysis .settings .verbose_output a
{
  background-image: url( ../../img/ico/ui-check-box-uncheck.png );
  background-position: 0 50%;
  color: #4D4D4D;
  display: block;
  padding-left: 21px;
}

#content #analysis .settings .verbose_output.active a
{
  background-image: url( ../../img/ico/ui-check-box.png );
}

#content #analysis .index label,
#content #analysis .query label
{
  display: block;
}

#content #analysis .index textarea,
#content #analysis .query textarea
{
  display: block;
  width: 100%;
}

#content #analysis .index
{
  float: left;
  margin-right: 0.5%;
  min-width: 47%;
  max-width: 99%;
}

#content #analysis .query
{
  float: right;
  margin-left: 0.5%;
  min-width: 47%;
  max-width: 99%;
}

#content #analysis .analysis-error
{
  background-color: #f00;
  background-image: url( ../../img/ico/construction.png );
  background-position: 10px 50%;
  color: #fff;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 10px;
  padding-left: 35px;
}

#content #analysis .analysis-error .head a
{
  color: #fff;
  cursor: auto;
}

#content #analysis #analysis-result
{
  overflow: auto;
}

#content #analysis #analysis-result .index,
#content #analysis #analysis-result .query
{
  background-color: #fff;
  padding-top: 20px;
}

#content #analysis #analysis-result table
{
  border-collapse: collapse;
}

#content #analysis #analysis-result td
{
  vertical-align: top;
  white-space: nowrap;
}

#content #analysis #analysis-result td.part.analyzer div,
#content #analysis #analysis-result td.part.spacer .holder,
#content #analysis #analysis-result td td td
{
  padding-top: 1px;
  padding-bottom: 1px;
}

#content #analysis #analysis-result.verbose_output td.legend
{
  display: table-cell;
}

#content #analysis #analysis-result.verbose_output td.data tr.verbose_output
{
  display: table-row;
}

#content #analysis #analysis-result .match
{
  background-color: #F0D9C3;
}

#content #analysis #analysis-result td.part
{
  padding-bottom: 10px;
}

#content #analysis #analysis-result td.part.analyzer div
{
  border-right: 1px solid #f0f0f0;
  padding-right: 10px;
}

#content #analysis #analysis-result td.part.analyzer abbr
{
  color: #4D4D4D;
}

#content #analysis #analysis-result td.part.legend .holder,
#content #analysis #analysis-result td.part.data .holder
{
  padding-left: 10px;
  padding-right: 10px;
  border-right: 1px solid #c0c0c0;
}

#content #analysis #analysis-result td.part.legend td
{
  color: #4D4D4D;
}

#content #analysis #analysis-result td.part.legend .holder
{
  border-right-color: #f0f0f0;
}

#content #analysis #analysis-result td.part.data:last-child .holder
{
  padding-right: 0;
  border-right: 0;
}

#content #analysis #analysis-result td.details
{
  padding-left: 10px;
  padding-right: 10px;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
}

#content #analysis #analysis-result td.details:first-child
{
  padding-left: 0;
  border-left: 0;
}

#content #analysis #analysis-result td.details:last-child
{
  padding-right: 0;
  border-right: 0;
}

#content #analysis #analysis-result td.details tr.empty td
{
  color: #f0f0f0;
}

#content #analysis #analysis-result td.details tr.raw_bytes td
{
  letter-spacing: -1px;
}

#content #analysis #analysis-result .part table table td
{
  border-top: 1px solid #f0f0f0;
}

#content #analysis #analysis-result .part table table tr:first-child td
{
  border-top: 0;
}

#content #analysis #field-analysis h2 { background-image: url( ../../img/ico/receipt.png ); }
#content #analysis .analysis-result h2 { background-image: url( ../../img/ico/receipt-invoice.png ); }
