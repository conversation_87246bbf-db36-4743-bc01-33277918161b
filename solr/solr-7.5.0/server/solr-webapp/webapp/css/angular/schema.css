/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #schema .loader
{
  background-position: 0 50%;
  padding-left: 21px;
}

#content #schema.loaded
{
  background-image: url( ../../img/div.gif );
  background-position: 21% 0;
  background-repeat: repeat-y;
}

#content #schema #data
{
  float: right;
  width: 78%;
}

#content #schema #related
{
  float: left;
  width: 20%;
}

#content #schema #related select
{
  width: 100%;
}

#content #schema #related select optgroup
{
  font-style: normal;
  padding: 5px;
}

#content #schema #related select option
{
  padding-left: 10px;
}

#content #schema #related #f-df-t
{
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

#content #schema #related .ukf-dsf dt
{
}

#content #schema #related dl
{
  margin-top: 15px;
}

#content #schema #related dl dt,
#content #schema #related dl dd a
{
  color: #4D4D4D;
}

#content #schema #related dl dt
{
  font-weight: bold;
  margin-top: 5px;
}

#content #schema #related dl dd a
{
  display: block;
  padding-left: 10px;
}

#content #schema #related dl dd a:hover
{
  background-color: #f8f8f8;
}

#content #schema #related .field .field,
#content #schema #related .field .field a,
#content #schema #related .dynamic-field .dynamic-field,
#content #schema #related .dynamic-field .dynamic-field a,
#content #schema #related .type .type,
#content #schema #related .type .type a,
#content #schema #related .active,
#content #schema #related .active a
{
  color: #333;
}

#content #schema #related .copyfield,
#content #schema #related .copyfield a
{
  color: #666;
}

#content #schema #data
{
}

#content #schema #data #index dt
{
  float: left;
  margin-right: 5px;
  width: 150px;
}

#content #schema #data #field .field-options
{
  margin-bottom: 10px;
}

#content #schema #data #field .field-options .head h2
{
  padding-left: 5px;
}

#content #schema #data #field .partial
{
}

#content #schema #data #field .partial p
{
  background-image: url( ../../img/ico/exclamation-button.png );
  background-position: 0 50%;
  padding-left: 21px;
}

#content #schema #data #field .field-options .options dt,
#content #schema #data #field .field-options .options dd
{
  float: left;
}

#content #schema #data #field .field-options .options dt
{
  clear: left;
  margin-right: 5px;
  width: 100px;
}

#content #schema #data #field .field-options .flags
{
  margin-top: 10px;
  margin-bottom: 20px;
}

#content #schema #data #field .field-options .flags thead td
{
  color: #4D4D4D;
  padding-right: 5px;
  width: 100px;
}

#content #schema #data #field .field-options .flags tbody td,
#content #schema #data #field .field-options .flags th
{
  padding: 2px 5px;
}

#content #schema #data #field .field-options .flags thead td,
#content #schema #data #field .field-options .flags tbody th
{
  padding-left: 0;
}

#content #schema #data #field .field-options .flags thead th,
#content #schema #data #field .field-options .flags tbody td
{
  border-left: 1px solid #f0f0f0;
}

#content #schema #data #field .field-options .flags tbody th,
#content #schema #data #field .field-options .flags tbody td
{
  border-top: 1px solid #f0f0f0;
}

#content #schema #data #field .field-options .flags tbody .check
{
  background-color: #fafdfa;
  background-image: url( ../../img/ico/tick.png );
  background-position: 50% 50%;
  text-align: center;
}

#content #schema #data #field .field-options .flags tbody .check span
{
}

#content #schema #data #field .field-options .flags tbody .text
{
  color: #4D4D4D;
}

#content #schema #data #field .field-options .analyzer,
#content #schema #data #field .field-options .analyzer li,
#content #schema #data #field .field-options .analyzer ul,
#content #schema #data #field .field-options .analyzer ul li
{
}

#content #schema #data #field .field-options .analyzer p,
#content #schema #data #field .field-options .analyzer dl
{
  float: left;
}

#content #schema #data #field .field-options .analyzer p
{
  margin-right: 5px;
  text-align: right;
  width: 125px;
  white-space: pre;
}

#content #schema #data #field .field-options .analyzer p a
{
  cursor: auto;
}

#content #schema #data #field .field-options .analyzer p a.analysis
{
  cursor: pointer;
  display: block;
}

#content #schema #data #field .field-options .analyzer p a.analysis span
{
  background-image: url( ../../img/ico/question-white.png );
  background-position: 0 50%;
  padding-left: 21px;
}

#content #schema #data #field .field-options .analyzer p a.analysis:hover span
{
  background-image: url( ../../img/ico/question.png );
  color: #008;
}

#content #schema #data #field .field-options .analyzer a
{
  cursor: auto;
}

#content #schema #data #field .field-options .analyzer .toggle
{
  background-image: url( ../../img/ico/chevron-small-expand.png );
  background-position: 100% 50%;
  cursor: pointer;
  display: block;
  padding-right: 21px;
}

#content #schema #data #field .field-options .analyzer .open .toggle
{
  background-image: url( ../../img/ico/chevron-small.png );
}

#content #schema #data #field .field-options .analyzer li
{
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
  padding-top: 10px;
}

#content #schema #data #field .field-options .analyzer ul
{
  clear: left;
  margin-left: 55px;
  padding-top: 5px;
}

#content #schema #data #field .field-options .analyzer .open ul
{
  display: block;
}

#content #schema #data #field .field-options .analyzer ul li
{
  border-top: 1px solid #f8f8f8;
  margin-top: 5px;
  padding-top: 5px;
}

#content #schema #data #field .field-options .analyzer ul p
{
  color: #4D4D4D;
  margin-right: 5px;
  text-align: right;
  width: 70px;
}

#content #schema #data #field .field-options .analyzer ul dd
{
  margin-left: 20px;
}

#content #schema #data #field .field-options .analyzer ul dd
{
  background-image: url( ../../img/ico/document-list.png );
  background-position: 0 50%;
  color: #4D4D4D;
  padding-left: 21px;
}

#content #schema #data #field .field-options .analyzer ul dd.ico-0
{
  background-image: url( ../../img/ico/slash.png );
}

#content #schema #data #field .field-options .analyzer ul dd.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #schema #data #field .head
{
  margin-bottom: 5px;
}

#content #schema #data #field .terminfo-holder
{
  border-top: 1px solid #c0c0c0;
  padding-top: 10px;
}

#content #schema #data #field .terminfo-holder .trigger
{
  float: left;
  width: 140px;
}

#content #schema #data #field .terminfo-holder .trigger button span
{
  background-image: url( ../../img/ico/information.png );
}

#content #schema #data #field .terminfo-holder .status
{
  border-left: 1px solid #f0f0f0;
  float: left;
  padding-left: 20px;
  padding-right: 20px;
}

#content #schema #data #field .terminfo-holder.disabled .trigger button span
{
  background-image: url( ../../img/ico/prohibition.png );
}

#content #schema #data #field .terminfo-holder.disabled .status
{
  display: block;
}

#content #schema #data #field .terminfo-holder .trigger .autoload
{
}

#content #schema #data #field .terminfo-holder.loaded .trigger .autoload
{
  background-image: url( ../../img/ico/ui-check-box-uncheck.png );
  background-position: 0 50%;
  color: #8D8D8D;
  display: block;
  margin-top: 10px;
  padding-left: 21px;
}

#content #schema #data #field .terminfo-holder .trigger .autoload:hover
{
  color: #008;
}

#content #schema #data #field .terminfo-holder .trigger .autoload.on
{
  background-image: url( ../../img/ico/ui-check-box.png );
  color: #333;
}

#content #schema #data #field .topterms-holder,
#content #schema #data #field .histogram-holder
{
  border-left: 1px solid #f0f0f0;
  float: left;
  padding-left: 20px;
  padding-right: 20px;
}

#content #schema #data #field .topterms-holder .head input
{
  height: 18px;
  line-height: 16px;
  text-align: right;
  width: 30px;
}

#content #schema #data #field .topterms-holder .head .max-holder
{
  color: #4D4D4D;
}

#content #schema #data #field .topterms-holder .head .max-holder:hover .max
{
  color: #008;
}

#content #schema #data #field .topterms-holder .head #query_link
{
  background-image: url( ../../img/ico/question-white.png );
  background-position: 0 50%;
  color: #4D4D4D;
  padding-left: 21px;
  margin-left: 5px;
}

#content #schema #data #field .topterms-holder .head #query_link:hover
{
  background-image: url( ../../img/ico/question.png );
}


#content #schema #data #field .topterms-holder .head #query_link span
{
  visibility: hidden;
}

#content #schema #data #field .topterms-holder .head #query_link:hover span
{
  visibility: visible;
}

#content #schema .topterms-holder li
{
  border-top: 1px solid  #999;
  margin-bottom: 5px;
}

/* possible overwrite with inline style */
#content #schema .topterms-holder li p
{
  background-color:  #999;
  color: #fff;
  float: left;
}

#content #schema .topterms-holder li p span
{
  display: block;
  padding-right: 2px;
  text-align: right;
}

/* possible overwrite with inline style */
#content #schema .topterms-holder li ul
{
  margin-left: 30px;
}

#content #schema .topterms-holder li li
{
  border-top: 0;
  margin-bottom: 0;
  white-space: nowrap;
}

#content #schema .topterms-holder li li.odd
{
  background-color: #f0f0f0;
}

#content #schema .topterms-holder li li a
{
  display: block;
  padding-left: 2px;
  padding-right: 2px;
}

#content #schema .topterms-holder li li a:hover
{
  background-color: #c0c0c0;
}

#content #schema #data #field .histogram-holder ul
{
  margin-left: 25px;
}

#content #schema #data #field .histogram-holder li
{
  margin-bottom: 2px;
  position: relative;
  width: 150px;
}

#content #schema #data #field .histogram-holder li.odd
{
  background-color: #f0f0f0;
}

#content #schema #data #field .histogram-holder li dl,
#content #schema #data #field .histogram-holder li dt
{
  padding-top: 1px;
  padding-bottom: 1px;
}

#content #schema #data #field .histogram-holder li dl
{
  background-color: #c0c0c0;
  min-width: 1px;
}

#content #schema #data #field .histogram-holder li dt
{
  color: #a0a0a0;
  position: absolute;
  overflow: hidden;
  left: -25px;
  top: 0px;
}

#content #schema #data #field .histogram-holder li dt span
{
  display: block;
  padding-right: 4px;
  text-align: right;
}

#content #schema #data #field .histogram-holder li dd
{
  clear: left;
  float: left;
  margin-left: 2px;
  white-space: nowrap;
}

#content #schema #data #field .histogram-holder li:hover dl
{
  background-color: #b0b0b0;
}

#content #schema #data #field .histogram-holder li:hover dt
{
  color: #333;
}

#content #schema #actions {
  margin-bottom: 20px;
  min-height: 30px;
}

#content #schema .actions #addField span { background-image: url( ../../img/ico/document-list.png ); }
#content #schema .actions #addDynamicField span { background-image: url( ../../img/ico/documents-stack.png ); }
#content #schema .actions #addCopyField span { background-image: url( ../../img/ico/document-import.png ); }

#content #schema .actions div.action
{
  width: 320px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 5px 5px 10px #c0c0c0;
  -moz-box-shadow: 5px 5px 10px #c0c0c0;
  -webkit-box-shadow: 5px 5px 10px #c0c0c0;
  position: absolute;
  left: 160px;
  top: 50px;
  padding: 10px;
  z-index: 2;
}

#content #schema .actions p
{
  padding-bottom: 8px;
}

#content #schema .actions label
{
  float: left;
  padding-top: 3px;
  padding-bottom: 3px;
  text-align: right;
  width: 25%;
}

#content #schema .actions input,
#content #schema .actions select,
#content #schema .actions .buttons,
#content #schema .actions .note span
{
  float: right;
  width: 71%;
}

#content #schema .actions label.checkbox {
  margin-left: 27%;
  text-align: left;
  width: 73%;
  padding: 0px;
  margin-top: 0px;
}
#content #schema .actions .checkbox input {
  float: none;
  width: auto;
}

#content #schema .add_showhide {
  background-image: url( ../../img/ico/chevron-small-expand.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
}

#content #schema .add_showhide.open {
    background-image: url( ../../img/ico/chevron-small.png );
}

#content #schema label
{
  cursor: pointer;
  display: block;
  margin-top: 5px;
  width: 100%;
}

#content #schema .checkbox
{
  margin-bottom: 0;
  width: auto;
}

#content #schema .chosen-container {
  margin-left: 6px;
  width: 100%;
}
#content #schema .chosen-drop input,
#content #schema .chosen-results {
  width: 100% !important;
}

#content #schema button span
{
  background-image: url( ../../img/ico/cross.png );
}
#content #schema button.submit span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #schema .error
{
  background-image: url( ../../img/ico/cross-button.png );
  background-position: 22% 1px;
  color: #c00;
  font-weight: bold;
  margin-bottom: 10px;
}

#content #schema #actions .error span
{
  float: right;
  width: 71%;
  padding-left: 3px;
  padding-right: 3px;
}

#content #schema .delete-field button span {
  background-image: url( ../../img/ico/cross.png );
}

#content #schema span.rem {
  background-image: url( ../../img/ico/cross.png );
  background-position: 100% 50%;
  cursor: pointer;
  padding-right: 21px;
  right:10px;
  float:right;
}

#content #schema .copyfield .updatable a {
  float:left;
  width:80%;
}

#content #schema dd.similarity.ng-binding::after {
  content: attr(data-tip) ;

  font-size: 12px;
  position: relative;
  white-space: nowrap;
  bottom: 9999px;
  left: 0;
  background: lightyellow;
  color: black;
  padding: 4px 7px;
  line-height: 24px;
  height: 24px;
  border: 1px solid darkgray;
  opacity: 0;
  transition:opacity 0.4s ease-out;
}

#content #schema dd.similarity.ng-binding:hover::after {
  opacity: 90;
  bottom: -20px;
}
