#**
 *  Check if Clustering is Enabled and then
 *  call cluster_results.vm
 *#

<h2 #annTitle("Clusters generated by Carrot2 using the /clustering RequestHandler")>
  Clusters
</h2>

## Div tag has placeholder text by default
<div id="clusters">
  Run Solr with option -Dsolr.clustering.enabled=true to see clustered search results.
</div>

## Replace the div content *if* Carrot^2 is available
<script type="text/javascript">
  $('#clusters').load("#url_for_solr/clustering#lens",
    {'wt':'velocity', 'v.template':"cluster_results"});
</script>
